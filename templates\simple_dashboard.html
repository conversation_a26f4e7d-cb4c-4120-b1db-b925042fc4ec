<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, viewport-fit=cover">
    <meta name="apple-mobile-web-app-capable" content="yes">
    <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent">
    <meta name="mobile-web-app-capable" content="yes">
    <meta name="theme-color" content="#212529">
    <title>TradingView自动交易系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.7.2/font/bootstrap-icons.css" rel="stylesheet">
    <link href="/static/css/style.css" rel="stylesheet">
    <style>
        .status-indicator {
            display: inline-block;
            width: 10px;
            height: 10px;
            border-radius: 50%;
            margin-right: 5px;
        }
        .status-online {
            background-color: #28a745;
            box-shadow: 0 0 5px rgba(40, 167, 69, 0.5);
        }
        .status-offline {
            background-color: #dc3545;
            box-shadow: 0 0 5px rgba(220, 53, 69, 0.5);
        }
        .metric-card {
            transition: transform 0.2s;
        }
        .metric-card:hover {
            transform: translateY(-2px);
        }

        /* 价格卡片样式 */
        .price-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            padding: 15px;
            text-align: center;
            color: white;
            transition: all 0.3s ease;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }

        .price-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.15);
        }

        .price-symbol {
            font-size: 0.9rem;
            font-weight: 600;
            opacity: 0.9;
            margin-bottom: 8px;
        }

        .price-value {
            font-size: 1.2rem;
            font-weight: 700;
            margin-bottom: 5px;
        }

        .price-change {
            font-size: 0.8rem;
            font-weight: 500;
        }

        .price-change.positive {
            color: #28a745;
            font-weight: 600;
        }

        .price-change.negative {
            color: #dc3545;
            font-weight: 600;
        }

        .price-change.neutral {
            color: #ffffff;
            opacity: 0.9;
            font-weight: 500;
        }

        /* 价格更新动画 */
        .price-update {
            animation: priceFlash 0.5s ease-in-out;
        }

        @keyframes priceFlash {
            0% { background-color: rgba(255, 255, 0, 0.3); }
            100% { background-color: transparent; }
        }

        /* 状态显示样式 */
        .status-item {
            text-align: center;
            padding: 10px;
            border-radius: 8px;
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            transition: all 0.3s ease;
        }

        .status-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        .status-label {
            font-size: 0.9rem;
            font-weight: 600;
            color: #6c757d;
            margin-bottom: 8px;
        }

        .status-value {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            font-size: 1rem;
            font-weight: 500;
        }

        .status-indicator {
            width: 12px;
            height: 12px;
            border-radius: 50%;
            display: inline-block;
            animation: pulse 2s infinite;
        }

        .status-indicator.status-online {
            background-color: #28a745;
            box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
        }

        .status-indicator.status-offline {
            background-color: #dc3545;
            box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.7);
        }

        .status-indicator.status-warning {
            background-color: #ffc107;
            box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
        }

        .status-indicator.status-paused {
            background-color: #6c757d;
            box-shadow: 0 0 0 0 rgba(108, 117, 125, 0.7);
        }

        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(40, 167, 69, 0.7);
            }
            70% {
                box-shadow: 0 0 0 10px rgba(40, 167, 69, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(40, 167, 69, 0);
            }
        }
        .metric-value {
            font-size: 1.5rem;
            font-weight: bold;
        }
        .metric-label {
            font-size: 0.875rem;
            color: #6c757d;
        }
        .btn-action {
            margin: 2px;
        }
        .table-responsive {
            border-radius: 0.375rem;
        }
        .symbol-row:hover {
            background-color: rgba(0, 123, 255, 0.05);
        }
        .config-section {
            margin-bottom: 2rem;
        }
        .nav-pills .nav-link {
            border-radius: 0.5rem;
            margin: 0 0.25rem;
        }
        .nav-pills .nav-link.active {
            background-color: #0d6efd;
        }
        /* 外汇交易时段一行布局样式 */
        .countdown-inline {
            font-size: 0.8rem;
            font-weight: 600;
            color: #fd7e14;
            font-family: 'Courier New', monospace;
        }

        .countdown-inline.countdown-soon {
            color: #ffc107;
            animation: pulse-countdown 2s infinite;
        }

        .countdown-inline.countdown-urgent {
            color: #dc3545;
            animation: pulse-countdown 1s infinite;
        }

        @keyframes pulse-countdown {
            0%, 100% { opacity: 1; transform: scale(1); }
            50% { opacity: 0.7; transform: scale(1.05); }
        }

        .market-indicator {
            display: flex;
            align-items: center;
            gap: 0.25rem;
            padding: 0.2rem 0.4rem;
            border-radius: 4px;
            background: rgba(0,0,0,0.05);
            transition: background-color 0.2s ease;
        }

        .market-indicator:hover {
            background: rgba(0,0,0,0.1);
        }

        .market-indicator .market-flag {
            font-size: 0.9rem;
        }

        .market-indicator .market-status {
            font-size: 0.8rem;
            font-weight: bold;
        }

        .market-indicator .market-status.active {
            color: #28a745;
            animation: pulse-dot 2s infinite;
        }

        .market-indicator .market-status.inactive {
            color: #dc3545;
        }

        @keyframes pulse-dot {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }

        /* 系统控制面板紧凑样式 */
        .control-group {
            display: flex;
            align-items: center;
            margin-bottom: 0.25rem;
        }

        .control-group .btn-group-sm .btn {
            padding: 0.25rem 0.5rem;
            font-size: 0.8rem;
            border-radius: 0.25rem;
        }

        .control-group .btn-group-sm .btn i {
            font-size: 0.9rem;
        }

        .vr {
            width: 1px;
            height: 2rem;
            background-color: #dee2e6;
            opacity: 0.5;
        }

        #quickSystemStatus {
            font-size: 0.75rem;
            padding: 0.25rem 0.5rem;
        }

        /* 系统状态底部栏样式 */
        .system-status-footer {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
            border-top: 2px solid #e9ecef;
            padding: 0.75rem 1rem;
            box-shadow: 0 -4px 20px rgba(0,0,0,0.1);
            z-index: 1030;
            backdrop-filter: blur(10px);
            transition: transform 0.3s ease;
        }

        .system-status-footer.hidden {
            transform: translateY(100%);
        }

        .system-status-footer .status-item {
            background: rgba(255,255,255,0.9);
            border: 1px solid #dee2e6;
            border-radius: 8px;
            padding: 0.5rem 0.75rem;
            margin-right: 0.5rem;
            margin-bottom: 0.25rem;
            font-size: 0.85rem;
            white-space: nowrap;
            transition: all 0.2s ease;
            color: #212529;
            font-weight: 500;
        }

        .system-status-footer .status-item:hover {
            background: rgba(255,255,255,1);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .system-status-footer .status-item i {
            margin-right: 0.25rem;
            font-size: 0.9rem;
        }

        .footer-btn {
            padding: 0.375rem 0.5rem;
            font-size: 0.8rem;
            border-radius: 6px;
            margin-left: 0.25rem;
            transition: all 0.2s ease;
        }

        .footer-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.15);
        }

        /* 为底部栏预留空间 */
        body.has-footer {
            padding-bottom: 80px;
        }

        /* 持仓管理页面紧凑样式 */
        .positions-compact .card-body {
            padding: 0.75rem;
        }

        .positions-compact .card-header {
            padding: 0.5rem 0.75rem;
        }

        .positions-compact .row {
            margin-bottom: 0.5rem;
        }

        @media (max-width: 768px) {
            .metric-value {
                font-size: 1.25rem;
            }
            .btn-group-sm .btn {
                padding: 0.25rem 0.5rem;
                font-size: 0.75rem;
            }
            .market-schedule-compact {
                padding: 0.5rem;
            }
            .market-item {
                padding: 0.2rem 0.4rem;
            }
            .market-name {
                font-size: 0.75rem;
            }

            /* 持仓管理移动端优化 */
            .positions-compact .card-body {
                padding: 0.5rem;
            }

            .positions-compact .card-header {
                padding: 0.375rem 0.5rem;
            }

            .positions-compact .row {
                margin-bottom: 0.375rem;
            }

            .positions-compact h5, .positions-compact h6 {
                font-size: 0.9rem;
            }

            .positions-compact .card-title {
                font-size: 0.8rem;
            }

            /* 系统控制移动端优化 */
            .control-group {
                margin-bottom: 0.5rem;
                width: 100%;
                justify-content: space-between;
            }

            .control-group .btn-group-sm .btn {
                padding: 0.2rem 0.4rem;
                font-size: 0.75rem;
            }

            .vr {
                display: none !important;
            }

            #quickSystemStatus {
                display: none !important;
            }

            /* 外汇时段移动端优化 */
            .market-indicator {
                padding: 0.15rem 0.3rem;
            }

            .market-indicator .market-flag {
                font-size: 0.8rem;
            }

            .countdown-inline {
                font-size: 0.7rem;
            }
        }

            /* 移动端底部栏优化 - 更紧凑的设计 */
            .system-status-footer {
                padding: 0.375rem 0.5rem;
                font-size: 0.7rem;
                height: auto;
                min-height: 45px;
            }

            .system-status-footer .d-flex {
                flex-wrap: nowrap;
                overflow-x: auto;
                gap: 0.25rem;
            }

            .system-status-footer .status-item {
                padding: 0.2rem 0.4rem;
                margin-right: 0.2rem;
                margin-bottom: 0;
                font-size: 0.65rem;
                min-width: auto;
                flex-shrink: 0;
                border-radius: 4px;
            }

            .system-status-footer .status-item span:not(.status-indicator) {
                display: none;
            }

            .system-status-footer .status-item i {
                margin-right: 0;
                font-size: 0.75rem;
            }

            .footer-btn {
                padding: 0.2rem 0.3rem;
                font-size: 0.65rem;
                margin-left: 0.1rem;
                border-radius: 4px;
                min-width: 28px;
                height: 28px;
            }

            .footer-btn i {
                font-size: 0.75rem;
            }

            /* 移动端为底部栏预留更少空间 */
            body.has-footer {
                padding-bottom: 50px;
            }

            /* 只显示最重要的状态项 */
            .system-status-footer .status-item:nth-child(n+3) {
                display: none;
            }
        }

        @media (max-width: 480px) {
            .system-status-footer {
                padding: 0.25rem 0.375rem;
                min-height: 40px;
            }

            .system-status-footer .d-flex {
                gap: 0.15rem;
            }

            .system-status-footer .status-item {
                padding: 0.15rem 0.3rem;
                margin-right: 0.15rem;
                font-size: 0.6rem;
            }

            .footer-btn {
                padding: 0.15rem 0.25rem;
                font-size: 0.6rem;
                margin-left: 0.05rem;
                min-width: 24px;
                height: 24px;
            }

            .footer-btn i {
                font-size: 0.7rem;
            }

            /* 超小屏幕只显示最重要的状态和按钮 */
            .system-status-footer .status-item:nth-child(n+2) {
                display: none;
            }

            .footer-btn:nth-child(n+4) {
                display: none;
            }

            body.has-footer {
                padding-bottom: 45px;
            }
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="#">
                <i class="bi bi-graph-up"></i>
                TradingView自动交易系统
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link active" href="#" onclick="showPage('dashboard')" id="nav-dashboard">
                            <i class="bi bi-speedometer2"></i> 仪表板
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showPage('trades')" id="nav-trades">
                            <i class="bi bi-list-ul"></i> 交易记录
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showPage('positions')" id="nav-positions">
                            <i class="bi bi-pie-chart"></i> 持仓管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showPage('config')" id="nav-config">
                            <i class="bi bi-gear"></i> 配置管理
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="#" onclick="showPage('alerts')" id="nav-alerts">
                            <i class="bi bi-bell"></i> 警报历史
                        </a>
                    </li>
                </ul>
                <ul class="navbar-nav">
                    <li class="nav-item">
                        <span class="navbar-text me-3">
                            <span id="systemStatus" class="status-indicator status-offline"></span>
                            <span id="systemStatusText">检查中...</span>
                        </span>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i> 管理员
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="#" onclick="showSystemSettings()">
                                <i class="bi bi-sliders"></i> 系统设置
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="logout()">
                                <i class="bi bi-box-arrow-right"></i> 退出登录
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="container-fluid mt-4">
        <!-- 警报区域 -->
        <div id="alertContainer"></div>

        <!-- 仪表板页面 -->
        <div id="dashboardPage" class="page-content">



        <!-- 系统状态卡片 -->
        <div class="row mb-4">
            <div class="col-lg-2 col-md-4 col-6 mb-3">
                <div class="card metric-card">
                    <div class="card-body">
                        <div class="metric-value text-primary" id="accountBalance">$0.00</div>
                        <div class="metric-label">账户余额</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-6 mb-3">
                <div class="card metric-card">
                    <div class="card-body">
                        <div class="metric-value text-info" id="accountEquity">$0.00</div>
                        <div class="metric-label">账户净值</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-6 mb-3">
                <div class="card metric-card">
                    <div class="card-body">
                        <div class="metric-value text-success" id="floatingPnL">$0.00</div>
                        <div class="metric-label">浮动盈亏</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-6 mb-3">
                <div class="card metric-card">
                    <div class="card-body">
                        <div class="metric-value text-secondary" id="freeMargin">$0.00</div>
                        <div class="metric-label">剩余保证金</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-6 mb-3">
                <div class="card metric-card">
                    <div class="card-body">
                        <div class="metric-value text-warning" id="marginLevel">0%</div>
                        <div class="metric-label">保证金比例</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-2 col-md-4 col-6 mb-3">
                <div class="card metric-card">
                    <div class="card-body">
                        <div class="metric-value text-info" id="openPositions">0</div>
                        <div class="metric-label">持仓数量</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第二行统计信息 -->
        <div class="row mb-4">
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card metric-card">
                    <div class="card-body">
                        <div class="metric-value text-success" id="todayProfit">$0.00</div>
                        <div class="metric-label">今日盈亏</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card metric-card">
                    <div class="card-body">
                        <div class="metric-value text-warning" id="todayAlerts">0</div>
                        <div class="metric-label">今日警报</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card metric-card">
                    <div class="card-body">
                        <div class="metric-value text-success" id="profitablePositions">0</div>
                        <div class="metric-label">盈利订单</div>
                    </div>
                </div>
            </div>
            <div class="col-lg-3 col-md-6 mb-3">
                <div class="card metric-card">
                    <div class="card-body">
                        <div class="metric-value text-danger" id="lossPositions">0</div>
                        <div class="metric-label">亏损订单</div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 实时价格显示 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="bi bi-graph-up text-success"></i> 实时价格</h6>
                        <button class="btn btn-outline-secondary btn-sm" onclick="refreshPrices()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                    <div class="card-body">
                        <div class="row" id="priceContainer">
                            <!-- 价格卡片将通过JavaScript动态生成 -->
                            <div class="col-lg-2 col-md-4 col-6 mb-3">
                                <div class="price-card">
                                    <div class="price-symbol">BTCUSD</div>
                                    <div class="price-value" id="price-BTCUSD">--</div>
                                    <div class="price-change" id="change-BTCUSD">--</div>
                                </div>
                            </div>
                            <div class="col-lg-2 col-md-4 col-6 mb-3">
                                <div class="price-card">
                                    <div class="price-symbol">ETHUSD</div>
                                    <div class="price-value" id="price-ETHUSD">--</div>
                                    <div class="price-change" id="change-ETHUSD">--</div>
                                </div>
                            </div>
                            <div class="col-lg-2 col-md-4 col-6 mb-3">
                                <div class="price-card">
                                    <div class="price-symbol">SOLUSD</div>
                                    <div class="price-value" id="price-SOLUSD">--</div>
                                    <div class="price-change" id="change-SOLUSD">--</div>
                                </div>
                            </div>
                            <div class="col-lg-2 col-md-4 col-6 mb-3">
                                <div class="price-card">
                                    <div class="price-symbol">XAUUSD</div>
                                    <div class="price-value" id="price-XAUUSD">--</div>
                                    <div class="price-change" id="change-XAUUSD">--</div>
                                </div>
                            </div>
                            <div class="col-lg-2 col-md-4 col-6 mb-3">
                                <div class="price-card">
                                    <div class="price-symbol">GBPJPY</div>
                                    <div class="price-value" id="price-GBPJPY">--</div>
                                    <div class="price-change" id="change-GBPJPY">--</div>
                                </div>
                            </div>
                            <div class="col-lg-2 col-md-4 col-6 mb-3">
                                <div class="price-card">
                                    <div class="price-symbol">更新时间</div>
                                    <div class="price-value" id="priceUpdateTime">--</div>
                                    <div class="price-change">实时刷新</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 警报统计 -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0"><i class="bi bi-bell-fill text-warning"></i> 警报统计</h6>
                        <button class="btn btn-outline-secondary btn-sm" onclick="refreshAlertStats()">
                            <i class="bi bi-arrow-clockwise"></i> 刷新
                        </button>
                    </div>
                    <div class="card-body">
                        <!-- 警报统计表格 - 一目了然显示所有时间段 -->
                        <div class="table-responsive">
                            <table class="table table-sm table-hover">
                                <thead class="table-light">
                                    <tr>
                                        <th rowspan="2" class="align-middle">交易对</th>
                                        <th colspan="2" class="text-center">1小时</th>
                                        <th colspan="2" class="text-center">4小时</th>
                                        <th colspan="2" class="text-center">24小时</th>
                                        <th rowspan="2" class="text-center align-middle">最新警报</th>
                                    </tr>
                                    <tr>
                                        <th class="text-center text-success">📈</th>
                                        <th class="text-center text-danger">📉</th>
                                        <th class="text-center text-success">📈</th>
                                        <th class="text-center text-danger">📉</th>
                                        <th class="text-center text-success">📈</th>
                                        <th class="text-center text-danger">📉</th>
                                    </tr>
                                </thead>
                                <tbody id="alertStatsTableBody">
                                    <tr>
                                        <td colspan="8" class="text-center text-muted">加载中...</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 外汇交易时段 -->
        <div class="row mb-2">
            <div class="col-12">
                <div class="card">
                    <div class="card-body py-2">
                        <div class="d-flex align-items-center justify-content-between flex-wrap">
                            <!-- 标题和时间 -->
                            <div class="d-flex align-items-center me-3 mb-1">
                                <h6 class="mb-0 me-2">
                                    <i class="bi bi-clock text-primary"></i> 外汇时段
                                </h6>
                                <small class="text-muted" id="currentTime">--</small>
                            </div>

                            <!-- 当前市场状态 -->
                            <div class="d-flex align-items-center me-3 mb-1">
                                <span class="badge bg-success me-2" id="currentMarketStatus">检测中</span>
                                <div class="text-nowrap">
                                    <small class="fw-bold" id="activeMarketName">当前市场</small>
                                    <small class="text-muted ms-1" id="activeMarketTime">--</small>
                                </div>
                            </div>

                            <!-- 下一个市场倒计时 -->
                            <div class="d-flex align-items-center me-3 mb-1">
                                <span class="badge bg-warning text-dark me-2" id="nextMarketStatus">等待</span>
                                <div class="text-nowrap">
                                    <small class="fw-bold" id="nextMarketName">下一个市场</small>
                                    <small class="countdown-inline ms-1" id="marketCountdown">--:--:--</small>
                                </div>
                            </div>

                            <!-- 市场状态指示器 -->
                            <div class="d-flex align-items-center gap-2 mb-1">
                                <div class="market-indicator" title="悉尼市场：06:00-15:00">
                                    <span class="market-flag">🇦🇺</span>
                                    <span class="market-status" id="sydney-status">●</span>
                                </div>
                                <div class="market-indicator" title="东京市场：08:00-17:00">
                                    <span class="market-flag">🇯🇵</span>
                                    <span class="market-status" id="tokyo-status">●</span>
                                </div>
                                <div class="market-indicator" title="伦敦市场：16:00-01:00">
                                    <span class="market-flag">🇬🇧</span>
                                    <span class="market-status" id="london-status">●</span>
                                </div>
                                <div class="market-indicator" title="纽约市场：21:00-06:00">
                                    <span class="market-flag">🇺🇸</span>
                                    <span class="market-status" id="newyork-status">●</span>
                                </div>
                                <small class="text-muted ms-2">GMT+8</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 系统控制面板 -->
        <div class="row mb-3">
            <div class="col-12">
                <div class="card">
                    <div class="card-header py-2">
                        <h6 class="mb-0">
                            <i class="bi bi-toggles"></i> 系统控制
                        </h6>
                    </div>
                    <div class="card-body py-2">
                        <div class="d-flex flex-wrap align-items-center gap-2">
                            <!-- 策略控制组 -->
                            <div class="control-group">
                                <small class="text-muted me-2">策略:</small>
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-success" onclick="startStrategy()" title="启动交易策略">
                                        <i class="bi bi-play-fill"></i>
                                        <span class="d-none d-md-inline ms-1">启动</span>
                                    </button>
                                    <button type="button" class="btn btn-warning" onclick="stopStrategy()" title="停止交易策略">
                                        <i class="bi bi-pause-fill"></i>
                                        <span class="d-none d-md-inline ms-1">停止</span>
                                    </button>
                                    <button type="button" class="btn btn-danger" onclick="emergencyStop()" title="紧急停止所有操作">
                                        <i class="bi bi-stop-fill"></i>
                                        <span class="d-none d-md-inline ms-1">紧急</span>
                                    </button>
                                </div>
                            </div>

                            <!-- 分隔线 -->
                            <div class="vr d-none d-md-block"></div>

                            <!-- 系统操作组 -->
                            <div class="control-group">
                                <small class="text-muted me-2">系统:</small>
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-info" onclick="refreshData()" title="刷新所有数据">
                                        <i class="bi bi-arrow-clockwise"></i>
                                        <span class="d-none d-lg-inline ms-1">刷新</span>
                                    </button>
                                    <button type="button" class="btn btn-secondary" onclick="showConfigModal()" title="系统配置">
                                        <i class="bi bi-gear"></i>
                                        <span class="d-none d-lg-inline ms-1">配置</span>
                                    </button>
                                </div>
                            </div>

                            <!-- 分隔线 -->
                            <div class="vr d-none d-md-block"></div>

                            <!-- 持仓操作组 -->
                            <div class="control-group">
                                <small class="text-muted me-2">持仓:</small>
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-success" onclick="closeProfitablePositions()" title="平仓所有盈利订单">
                                        <i class="bi bi-check-circle"></i>
                                        <span class="d-none d-lg-inline ms-1">盈利</span>
                                    </button>
                                    <button type="button" class="btn btn-outline-warning" onclick="closeLossPositions()" title="平仓所有亏损订单">
                                        <i class="bi bi-exclamation-circle"></i>
                                        <span class="d-none d-lg-inline ms-1">亏损</span>
                                    </button>
                                    <button type="button" class="btn btn-outline-danger" onclick="closeAllPositions()" title="平仓所有订单">
                                        <i class="bi bi-x-circle"></i>
                                        <span class="d-none d-lg-inline ms-1">全部</span>
                                    </button>
                                </div>
                            </div>

                            <!-- 快速状态显示 -->
                            <div class="ms-auto d-none d-lg-flex align-items-center">
                                <small class="text-muted me-2">状态:</small>
                                <span class="badge bg-secondary" id="quickSystemStatus">检查中</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- 实时数据 -->
        <div class="row">
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-graph-up"></i> 当前持仓
                        </h5>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive" style="overflow-x: auto !important; -webkit-overflow-scrolling: touch;">
                            <table class="table table-hover" id="positionsTable" style="min-width: 800px;">
                                <thead>
                                    <tr>
                                        <th>交易对</th>
                                        <th>方向</th>
                                        <th>手数</th>
                                        <th>开仓价</th>
                                        <th>当前价</th>
                                        <th>盈亏</th>
                                        <th>止盈/止损</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="positionsTableBody">
                                    <tr>
                                        <td colspan="8" class="text-center text-muted">暂无持仓</td>
                                    </tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-lg-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="bi bi-bell"></i> 最新警报
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="recentAlerts">
                            <div class="text-center text-muted">暂无警报</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        </div>

        <!-- 其他页面内容 -->
        <div id="tradesPage" class="page-content" style="display: none;"></div>
        <div id="positionsPage" class="page-content" style="display: none;"></div>
        <div id="configPage" class="page-content" style="display: none;"></div>
        <div id="alertsPage" class="page-content" style="display: none;"></div>
    </div>

    <!-- 系统状态底部栏 (仅首页显示) -->
    <div id="systemStatusFooter" class="system-status-footer" style="display: none;">
        <div class="d-flex justify-content-between align-items-center">
            <div class="d-flex align-items-center flex-wrap">
                <div class="status-item" title="交易系统状态">
                    <i class="bi bi-graph-up"></i>
                    <span class="status-indicator" id="footerTradingStatusIndicator"></span>
                    <span id="footerTradingStatusText">检查中...</span>
                </div>
                <div class="status-item" title="Webhook服务状态">
                    <i class="bi bi-cloud-arrow-down"></i>
                    <span class="status-indicator" id="footerWebhookStatusIndicator"></span>
                    <span id="footerWebhookStatusText">运行中</span>
                </div>
                <div class="status-item" title="MT5连接状态">
                    <i class="bi bi-pc-display"></i>
                    <span class="status-indicator" id="footerMt5StatusIndicator"></span>
                    <span id="footerMt5StatusText">已连接</span>
                </div>
                <div class="status-item" title="超时监控状态">
                    <i class="bi bi-shield-check"></i>
                    <span class="status-indicator" id="footerTimeoutMonitorIndicator"></span>
                    <span id="footerTimeoutMonitorText">运行中</span>
                </div>
            </div>
            <div class="btn-group">
                <button class="btn btn-success footer-btn" onclick="enableTrading()" id="footerEnableTradingBtn" title="启用交易">
                    <i class="bi bi-play-circle-fill"></i>
                </button>
                <button class="btn btn-warning footer-btn" onclick="pauseTrading()" id="footerPauseTradingBtn" title="暂停交易">
                    <i class="bi bi-pause-circle-fill"></i>
                </button>
                <button class="btn btn-info footer-btn" onclick="refreshSystemStatus()" title="刷新状态">
                    <i class="bi bi-arrow-clockwise"></i>
                </button>
                <button class="btn btn-secondary footer-btn" onclick="resetMT5Reconnect()" title="重置MT5">
                    <i class="bi bi-bootstrap-reboot"></i>
                </button>
                <button class="btn btn-primary footer-btn" onclick="debugRefreshPositions()" title="调试持仓">
                    <i class="bi bi-bug-fill"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        // 简化的UI管理
        class SimpleUI {
            constructor() {
                this.sessionToken = this.getStoredToken();
                this.init();
            }

            getStoredToken() {
                return localStorage.getItem('session_token') || sessionStorage.getItem('session_token');
            }

            init() {
                if (!this.sessionToken) {
                    window.location.href = '/login';
                    return;
                }
                this.loadData();
            }

            async apiCall(url, method = 'GET', data = null) {
                const options = {
                    method: method,
                    headers: {
                        'Authorization': `Bearer ${this.sessionToken}`,
                        'Content-Type': 'application/json'
                    }
                };

                if (data && method !== 'GET') {
                    options.body = JSON.stringify(data);
                }

                try {
                    const response = await fetch(url, options);
                    if (response.status === 401) {
                        this.logout();
                        return null;
                    }
                    if (response.status === 404) {
                        console.warn(`API端点不存在: ${url}`);
                        return { success: false, error: 'API端点不存在' };
                    }
                    if (!response.ok) {
                        console.error(`API调用失败: ${response.status} ${response.statusText}`);
                        return { success: false, error: `HTTP ${response.status}` };
                    }
                    return await response.json();
                } catch (error) {
                    console.error('API调用失败:', error);
                    // 不显示网络错误的警报，避免过多提示
                    return { success: false, error: error.message };
                }
            }

            async loadData() {
                try {
                    // 加载系统状态
                    const healthResponse = await this.apiCall('/api/system/health');
                    if (healthResponse && healthResponse.success) {
                        this.updateSystemStatus(healthResponse.data);
                    }

                    // 加载账户信息
                    const statusResponse = await this.apiCall('/api/system/status');
                    if (statusResponse && statusResponse.success) {
                        console.log('系统状态API响应:', statusResponse.data);
                        this.updateAccountInfo(statusResponse.data);
                    } else {
                        console.error('系统状态API失败:', statusResponse);
                    }

                    // 加载最新警报
                    this.loadRecentAlerts();

                    // 加载警报统计
                    this.loadAlertStats();

                    // 强制刷新持仓显示
                    setTimeout(() => {
                        this.forceUpdatePositionDisplay();
                    }, 1000);
                } catch (error) {
                    console.error('加载数据失败:', error);
                }
            }

            async loadRecentAlerts() {
                try {
                    // 获取最近1小时的警报
                    const response = await this.apiCall('/api/alerts?limit=10&hours=1');
                    if (response && response.success) {
                        // 确保使用正确的数据结构
                        const alerts = response.data.alerts || response.data || [];
                        this.updateRecentAlerts(alerts);
                    } else {
                        console.error('加载最新警报失败:', response);
                        this.updateRecentAlerts([]);
                    }
                } catch (error) {
                    console.error('加载最新警报异常:', error);
                    this.updateRecentAlerts([]);
                }
            }

            updateRecentAlerts(alerts) {
                const container = document.getElementById('recentAlerts');
                if (!container) return;

                if (alerts.length === 0) {
                    container.innerHTML = '<div class="text-center text-muted">最近1小时暂无警报</div>';
                    return;
                }

                const alertsHtml = alerts.map(alert => {
                    const time = formatToBeijingTime(alert.timestamp || alert.created_at);
                    // 使用正确的字段名
                    const symbol = alert.standard_symbol || alert.original_symbol;
                    const signal = alert.standard_signal || alert.original_signal;
                    const signalClass = signal === 'BUY' ? 'text-success' : 'text-danger';
                    const signalIcon = signal === 'BUY' ? '📈' : '📉';

                    return `
                        <div class="d-flex justify-content-between align-items-center mb-2 p-2 border-bottom">
                            <div>
                                <span class="fw-bold">${symbol}</span>
                                <span class="${signalClass} ms-2">${signalIcon} ${signal}</span>
                            </div>
                            <small class="text-muted">${time.split(' ')[1]}</small>
                        </div>
                    `;
                }).join('');

                container.innerHTML = alertsHtml;
            }

            async loadAlertStats() {
                try {
                    // 并行加载多个时间段的数据
                    const timeRanges = [1, 4, 24];
                    const promises = timeRanges.map(hours =>
                        this.apiCall(`/api/alerts/stats-direct?hours=${hours}`)
                    );

                    const responses = await Promise.all(promises);

                    // 整合数据
                    const allStats = {};
                    responses.forEach((response, index) => {
                        if (response && response.success) {
                            allStats[timeRanges[index]] = response.data || {};
                        } else {
                            allStats[timeRanges[index]] = {};
                        }
                    });

                    this.updateAlertStats(allStats);
                } catch (error) {
                    console.error('加载警报统计异常:', error);
                    this.updateAlertStats({});
                }
            }

            updateAlertStats(allStatsData) {
                const tbody = document.getElementById('alertStatsTableBody');
                if (!tbody) return;

                // 定义主要交易对
                const symbols = ['BTCUSD', 'ETHUSD', 'SOLUSD', 'XAUUSD', 'GBPJPY'];

                if (Object.keys(allStatsData).length === 0) {
                    tbody.innerHTML = '<tr><td colspan="8" class="text-center text-muted">暂无数据</td></tr>';
                    return;
                }

                const rows = symbols.map(symbol => {
                    // 获取各时间段的数据
                    const stats1h = allStatsData[1]?.[symbol] || { buy: 0, sell: 0, latest: null };
                    const stats4h = allStatsData[4]?.[symbol] || { buy: 0, sell: 0, latest: null };
                    const stats24h = allStatsData[24]?.[symbol] || { buy: 0, sell: 0, latest: null };

                    // 找到最新的警报时间
                    let latestTime = '--';
                    const latestTimes = [stats1h.latest, stats4h.latest, stats24h.latest].filter(t => t);
                    if (latestTimes.length > 0) {
                        const latest = latestTimes.sort().reverse()[0];
                        const time = formatToBeijingTime(latest);
                        latestTime = time.split(' ')[1]; // 只显示时间部分
                    }

                    return `
                        <tr>
                            <td class="fw-bold">${symbol}</td>
                            <!-- 1小时 -->
                            <td class="text-center">
                                <span class="badge bg-success-subtle text-success">${stats1h.buy || 0}</span>
                            </td>
                            <td class="text-center">
                                <span class="badge bg-danger-subtle text-danger">${stats1h.sell || 0}</span>
                            </td>
                            <!-- 4小时 -->
                            <td class="text-center">
                                <span class="badge bg-success-subtle text-success">${stats4h.buy || 0}</span>
                            </td>
                            <td class="text-center">
                                <span class="badge bg-danger-subtle text-danger">${stats4h.sell || 0}</span>
                            </td>
                            <!-- 24小时 -->
                            <td class="text-center">
                                <span class="badge bg-success">${stats24h.buy || 0}</span>
                            </td>
                            <td class="text-center">
                                <span class="badge bg-danger">${stats24h.sell || 0}</span>
                            </td>
                            <!-- 最新警报 -->
                            <td class="text-center">
                                <small class="text-muted">${latestTime}</small>
                            </td>
                        </tr>
                    `;
                }).join('');

                tbody.innerHTML = rows;
            }

            async loadAccountInfo() {
                try {
                    // 加载账户信息
                    const statusResponse = await this.apiCall('/api/system/status');
                    if (statusResponse && statusResponse.success) {
                        this.updateAccountInfo(statusResponse.data);
                    }
                } catch (error) {
                    console.error('加载账户信息失败:', error);
                }
            }

            updateAccountInfo(data) {
                // 更新首页账户信息
                if (data.account_info) {
                    const account = data.account_info;
                    document.getElementById('accountBalance').textContent = `$${account.balance.toFixed(2)}`;
                    document.getElementById('accountEquity').textContent = `$${account.equity.toFixed(2)}`;
                    document.getElementById('freeMargin').textContent = `$${account.free_margin.toFixed(2)}`;
                }

                // 更新持仓页面账户信息
                if (data.account_info) {
                    const account = data.account_info;
                    document.getElementById('accountBalanceDetail').textContent = `$${account.balance.toFixed(2)}`;
                    document.getElementById('accountEquityDetail').textContent = `$${account.equity.toFixed(2)}`;
                }

                // 更新浮动盈亏
                if (data.positions) {
                    const positions = data.positions;
                    let floatingPnL = 0;
                    positions.forEach(pos => {
                        floatingPnL += pos.profit || 0;
                    });

                    // 更新首页浮动盈亏
                    const floatingElement = document.getElementById('floatingPnL');
                    if (floatingElement) {
                        floatingElement.textContent = `$${floatingPnL.toFixed(2)}`;
                        if (floatingPnL > 0) {
                            floatingElement.className = 'metric-value text-success';
                        } else if (floatingPnL < 0) {
                            floatingElement.className = 'metric-value text-danger';
                        } else {
                            floatingElement.className = 'metric-value text-secondary';
                        }
                    }

                    // 更新持仓页面浮动盈亏
                    const totalFloatingPLElement = document.getElementById('totalFloatingPL');
                    if (totalFloatingPLElement) {
                        totalFloatingPLElement.textContent = `$${floatingPnL.toFixed(2)}`;
                        const floatingPLCard = totalFloatingPLElement.closest('.card');
                        if (floatingPLCard) {
                            floatingPLCard.className = 'card text-white ' +
                                (floatingPnL > 0 ? 'bg-success' :
                                 floatingPnL < 0 ? 'bg-danger' : 'bg-secondary');
                        }
                    }
                }
            }

            async forceUpdatePositionDisplay() {
                try {
                    const response = await this.apiCall('/api/positions');
                    if (response && response.success) {
                        const positions = response.data || [];
                        console.log('强制更新持仓显示:', positions);

                        // 直接更新持仓统计
                        const totalPositions = positions.length;
                        let floatingPnL = 0;
                        let profitableCount = 0;
                        let lossCount = 0;

                        positions.forEach(pos => {
                            const profit = pos.profit || 0;
                            floatingPnL += profit;

                            if (profit > 0) {
                                profitableCount++;
                            } else if (profit < 0) {
                                lossCount++;
                            }
                        });

                        // 更新页面显示
                        const openPositionsEl = document.getElementById('openPositions');
                        const profitablePositionsEl = document.getElementById('profitablePositions');
                        const lossPositionsEl = document.getElementById('lossPositions');
                        const floatingPnLEl = document.getElementById('floatingPnL');

                        if (openPositionsEl) openPositionsEl.textContent = totalPositions;
                        if (profitablePositionsEl) profitablePositionsEl.textContent = profitableCount;
                        if (lossPositionsEl) lossPositionsEl.textContent = lossCount;
                        if (floatingPnLEl) {
                            floatingPnLEl.textContent = `$${floatingPnL.toFixed(2)}`;
                            // 设置颜色
                            if (floatingPnL > 0) {
                                floatingPnLEl.className = 'metric-value text-success';
                            } else if (floatingPnL < 0) {
                                floatingPnLEl.className = 'metric-value text-danger';
                            } else {
                                floatingPnLEl.className = 'metric-value text-secondary';
                            }
                        }

                        console.log('持仓显示已更新:', {
                            totalPositions,
                            profitableCount,
                            lossCount,
                            floatingPnL
                        });

                        // 同时更新首页的持仓表格
                        renderPositionsTable(positions);
                    }
                } catch (error) {
                    console.error('强制更新持仓显示失败:', error);
                }
            }

            updateSystemStatus(data) {
                const statusIndicator = document.getElementById('systemStatus');
                const statusText = document.getElementById('systemStatusText');

                if (statusIndicator && statusText) {
                    // 检查MT5连接状态 - 支持多种数据格式
                    let mt5Connected = false;

                    if (data.components && data.components.mt5_connection !== undefined) {
                        // 健康检查API格式
                        mt5Connected = data.components.mt5_connection === true;
                    } else if (data.mt5_connection !== undefined) {
                        // 直接格式
                        mt5Connected = data.mt5_connection === true;
                    } else if (data.mt5_connected !== undefined) {
                        // 系统状态API格式
                        mt5Connected = data.mt5_connected === true;
                    }

                    if (mt5Connected) {
                        statusIndicator.className = 'status-indicator status-online';
                        statusText.textContent = 'MT5已连接';
                    } else {
                        statusIndicator.className = 'status-indicator status-offline';
                        statusText.textContent = 'MT5离线';
                    }

                    // 添加调试信息
                    console.log('系统状态更新:', {
                        data: data,
                        mt5Connected: mt5Connected
                    });
                }
            }

            updateAccountInfo(data) {
                console.log('updateAccountInfo 接收到的数据:', data);

                // 更新账户信息
                if (data.account_info) {
                    const account = data.account_info;

                    // 基础账户信息
                    document.getElementById('accountBalance').textContent = `$${(account.balance || 0).toFixed(2)}`;
                    document.getElementById('accountEquity').textContent = `$${(account.equity || 0).toFixed(2)}`;
                    document.getElementById('freeMargin').textContent = `$${(account.margin_free || 0).toFixed(2)}`;

                    // 保证金比例
                    const marginLevel = account.margin_level || 0;
                    document.getElementById('marginLevel').textContent = `${marginLevel.toFixed(1)}%`;

                    // 根据保证金比例设置颜色
                    const marginElement = document.getElementById('marginLevel');
                    if (marginLevel < 100) {
                        marginElement.className = 'metric-value text-danger';
                    } else if (marginLevel < 200) {
                        marginElement.className = 'metric-value text-warning';
                    } else {
                        marginElement.className = 'metric-value text-success';
                    }
                }

                // 更新持仓信息
                if (data.positions) {
                    const positions = data.positions;
                    const totalPositions = positions.length;
                    console.log('持仓数据:', positions, '数量:', totalPositions);

                    // 计算浮动盈亏和盈利/亏损订单数量
                    let floatingPnL = 0;
                    let profitableCount = 0;
                    let lossCount = 0;

                    positions.forEach(pos => {
                        const profit = pos.profit || 0;
                        floatingPnL += profit;

                        if (profit > 0) {
                            profitableCount++;
                        } else if (profit < 0) {
                            lossCount++;
                        }
                    });

                    // 更新显示
                    document.getElementById('openPositions').textContent = totalPositions;
                    document.getElementById('profitablePositions').textContent = profitableCount;
                    document.getElementById('lossPositions').textContent = lossCount;

                    // 浮动盈亏
                    const floatingElement = document.getElementById('floatingPnL');
                    floatingElement.textContent = `$${floatingPnL.toFixed(2)}`;

                    // 根据盈亏设置颜色
                    if (floatingPnL > 0) {
                        floatingElement.className = 'metric-value text-success';
                    } else if (floatingPnL < 0) {
                        floatingElement.className = 'metric-value text-danger';
                    } else {
                        floatingElement.className = 'metric-value text-secondary';
                    }
                }

                // 更新今日盈亏
                if (data.today_profit !== undefined) {
                    const todayProfitElement = document.getElementById('todayProfit');
                    const todayProfit = data.today_profit || 0;
                    todayProfitElement.textContent = `$${todayProfit.toFixed(2)}`;

                    // 根据盈亏设置颜色
                    if (todayProfit > 0) {
                        todayProfitElement.className = 'metric-value text-success';
                    } else if (todayProfit < 0) {
                        todayProfitElement.className = 'metric-value text-danger';
                    } else {
                        todayProfitElement.className = 'metric-value text-secondary';
                    }
                }

                // 更新今日警报数量
                if (data.today_alerts !== undefined) {
                    document.getElementById('todayAlerts').textContent = data.today_alerts || 0;
                }
            }

            showAlert(type, message, duration = 5000) {
                const alertContainer = document.getElementById('alertContainer');
                const alertId = 'alert_' + Date.now();
                
                alertContainer.innerHTML = `
                    <div id="${alertId}" class="alert alert-${type} alert-dismissible fade show" role="alert">
                        ${message}
                        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                    </div>
                `;

                if (duration > 0) {
                    setTimeout(() => {
                        const alert = document.getElementById(alertId);
                        if (alert) {
                            alert.remove();
                        }
                    }, duration);
                }
            }

            logout() {
                localStorage.removeItem('session_token');
                sessionStorage.removeItem('session_token');
                window.location.href = '/login';
            }
        }

        // 全局函数
        let ui;

        function logout() {
            if (ui) ui.logout();
        }

        function refreshData() {
            if (ui) {
                ui.loadData();
                ui.showAlert('info', '数据已刷新');
            }
        }

        // 页面管理
        function showPage(pageId) {
            // 隐藏所有页面
            document.querySelectorAll('.page-content').forEach(page => {
                page.style.display = 'none';
            });

            // 更新导航状态
            document.querySelectorAll('.nav-link').forEach(link => {
                link.classList.remove('active');
            });

            const navLink = document.getElementById(`nav-${pageId}`);
            if (navLink) {
                navLink.classList.add('active');
            }

            // 控制底部栏显示 - 只在首页显示
            const footer = document.getElementById('systemStatusFooter');
            const body = document.body;

            if (pageId === 'dashboard') {
                footer.style.display = 'block';
                body.classList.add('has-footer');
            } else {
                footer.style.display = 'none';
                body.classList.remove('has-footer');
            }

            // 显示目标页面
            const targetPage = document.getElementById(`${pageId}Page`);
            if (targetPage) {
                targetPage.style.display = 'block';

                // 根据页面ID加载内容
                switch(pageId) {
                    case 'dashboard':
                        // 仪表板内容已在HTML中
                        break;
                    case 'trades':
                        loadTradesPage();
                        break;
                    case 'positions':
                        loadPositionsPage();
                        break;
                    case 'config':
                        loadConfigPage();
                        break;
                    case 'alerts':
                        loadAlertsPage();
                        break;
                }

                // 页面切换后，延迟执行移动端表格优化
                setTimeout(() => {
                    if (window.innerWidth <= 768) {
                        optimizeMobileTableScroll();
                    }
                }, 300);
            }
        }

        function loadTradesPage() {
            const page = document.getElementById('tradesPage');
            page.innerHTML = `
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h5 class="mb-0"><i class="bi bi-list-ul"></i> 交易记录</h5>
                        <div class="btn-group">
                            <button class="btn btn-success btn-sm" onclick="syncAllOrders()">
                                <i class="bi bi-arrow-repeat"></i> 同步所有订单
                            </button>
                            <button class="btn btn-outline-primary btn-sm" onclick="refreshTrades()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- 筛选器 -->
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <select class="form-select form-select-sm" id="statusFilter" onchange="applyTradesFilter()">
                                    <option value="">全部状态</option>
                                    <option value="OPEN">开仓</option>
                                    <option value="CLOSED">已平仓</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select form-select-sm" id="symbolFilter" onchange="applyTradesFilter()">
                                    <option value="">全部交易对</option>
                                    <option value="ETHUSD">ETHUSD</option>
                                    <option value="BTCUSD">BTCUSD</option>
                                    <option value="EURUSD">EURUSD</option>
                                    <option value="GBPUSD">GBPUSD</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <select class="form-select form-select-sm" id="limitFilter" onchange="applyTradesFilter()">
                                    <option value="10">10条/页</option>
                                    <option value="20" selected>20条/页</option>
                                    <option value="50">50条/页</option>
                                </select>
                            </div>
                        </div>

                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>订单号</th>
                                        <th>交易对</th>
                                        <th>方向</th>
                                        <th>手数</th>
                                        <th>开仓价</th>
                                        <th>平仓价</th>
                                        <th>状态</th>
                                        <th>盈亏</th>
                                        <th>开仓时间</th>
                                        <th>平仓时间</th>
                                        <th>下单原因</th>
                                        <th>平仓原因</th>
                                        <th>同步状态</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="tradesTableBody">
                                    <tr><td colspan="14" class="text-center text-muted">加载中...</td></tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div id="tradesInfo" class="text-muted"></div>
                            <nav>
                                <ul class="pagination pagination-sm mb-0" id="tradesPagination"></ul>
                            </nav>
                        </div>
                    </div>
                </div>
            `;
            loadTradesData();
        }

        function loadPositionsPage() {
            const page = document.getElementById('positionsPage');
            page.innerHTML = `
                <div class="positions-compact">
                <!-- 账户基本情况 -->
                <div class="row mb-2">
                    <div class="col-lg-3 col-md-6 mb-2">
                        <div class="card bg-primary text-white">
                            <div class="card-body py-2">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title mb-1">账户余额</h6>
                                        <h5 id="accountBalanceDetail">$0.00</h5>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-wallet2 fs-3"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-2">
                        <div class="card bg-info text-white">
                            <div class="card-body py-2">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title mb-1">账户净值</h6>
                                        <h5 id="accountEquityDetail">$0.00</h5>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-graph-up fs-3"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-2">
                        <div class="card bg-success text-white">
                            <div class="card-body py-2">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title mb-1">浮动盈亏</h6>
                                        <h5 id="totalFloatingPL">$0.00</h5>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-currency-dollar fs-3"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6 mb-2">
                        <div class="card bg-warning text-white">
                            <div class="card-body py-2">
                                <div class="d-flex justify-content-between">
                                    <div>
                                        <h6 class="card-title mb-1">保证金</h6>
                                        <h5 id="accountMargin">$0.00</h5>
                                    </div>
                                    <div class="align-self-center">
                                        <i class="bi bi-shield-check fs-3"></i>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 持仓统计信息 -->
                <div class="row mb-2">
                    <div class="col-lg-2 col-md-4 col-6 mb-2">
                        <div class="card text-center">
                            <div class="card-body py-2">
                                <h6 class="text-primary mb-1" id="totalPositions">0</h6>
                                <small class="text-muted">总持仓数</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-2">
                        <div class="card text-center">
                            <div class="card-body py-2">
                                <h6 class="text-success mb-1" id="profitablePositions">0</h6>
                                <small class="text-muted">盈利订单</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-2">
                        <div class="card text-center">
                            <div class="card-body py-2">
                                <h6 class="text-danger mb-1" id="lossPositions">0</h6>
                                <small class="text-muted">亏损订单</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-2">
                        <div class="card text-center">
                            <div class="card-body py-2">
                                <h6 class="text-info mb-1" id="totalVolume">0.00</h6>
                                <small class="text-muted">总手数</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-2">
                        <div class="card text-center">
                            <div class="card-body py-2">
                                <h6 class="text-primary mb-1" id="buyPositions">0</h6>
                                <small class="text-muted">做多订单</small>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-2 col-md-4 col-6 mb-2">
                        <div class="card text-center">
                            <div class="card-body py-2">
                                <h6 class="text-secondary mb-1" id="sellPositions">0</h6>
                                <small class="text-muted">做空订单</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 监控控制面板 -->
                <div class="card mb-3">
                    <div class="card-header py-2">
                        <h6 class="mb-0">
                            <i class="bi bi-shield-check"></i> 超时平仓监控
                        </h6>
                    </div>
                    <div class="card-body py-2">
                        <div class="row align-items-center">
                            <div class="col-md-6">
                                <div class="d-flex align-items-center">
                                    <span class="me-3">监控状态:</span>
                                    <span id="monitoringStatus" class="badge bg-secondary">检查中...</span>
                                    <button id="toggleMonitoringBtn" class="btn btn-sm btn-outline-primary ms-3" onclick="toggleMonitoring()">
                                        <i class="bi bi-play-circle"></i> 启动监控
                                    </button>
                                </div>
                            </div>
                            <div class="col-md-6 text-end">
                                <small class="text-muted">
                                    监控中的订单: <span id="monitoredCount">0</span> 个
                                </small>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card">
                    <div class="card-header py-2">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <h6 class="mb-0"><i class="bi bi-pie-chart"></i> 持仓管理</h6>
                            <div class="btn-group">
                                <button class="btn btn-outline-primary btn-sm" onclick="showGlobalSlTpModal()">
                                    <i class="bi bi-gear-wide-connected"></i> 总持仓盈亏监控
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="refreshPositions()">
                                    <i class="bi bi-arrow-clockwise"></i> 刷新
                                </button>
                            </div>
                        </div>
                        <!-- 一键平仓操作按钮 -->
                        <div class="row g-2">
                            <div class="col-6 col-md-3">
                                <button class="btn btn-danger w-100" onclick="closeAllPositions()">
                                    <i class="bi bi-x-circle-fill"></i>
                                    <span class="d-none d-sm-inline">平仓所有</span>
                                    <span class="d-sm-none">全部</span>
                                </button>
                            </div>
                            <div class="col-6 col-md-3">
                                <button class="btn btn-success w-100" onclick="closeProfitablePositions()">
                                    <i class="bi bi-check-circle-fill"></i>
                                    <span class="d-none d-sm-inline">平仓盈利</span>
                                    <span class="d-sm-none">盈利</span>
                                </button>
                            </div>
                            <div class="col-6 col-md-3">
                                <button class="btn btn-warning w-100" onclick="closeLossPositions()">
                                    <i class="bi bi-exclamation-circle-fill"></i>
                                    <span class="d-none d-sm-inline">平仓亏损</span>
                                    <span class="d-sm-none">亏损</span>
                                </button>
                            </div>
                            <div class="col-6 col-md-3">
                                <button class="btn btn-info w-100" onclick="showPositionStats()">
                                    <i class="bi bi-bar-chart-fill"></i>
                                    <span class="d-none d-sm-inline">统计信息</span>
                                    <span class="d-sm-none">统计</span>
                                </button>
                            </div>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>订单号</th>
                                        <th>交易对</th>
                                        <th>方向</th>
                                        <th>手数</th>
                                        <th>开仓价</th>
                                        <th>当前价</th>
                                        <th>浮动盈亏</th>
                                        <th>操作</th>
                                        <th>平仓倒计时</th>
                                    </tr>
                                </thead>
                                <tbody id="positionsDetailTableBody">
                                    <tr><td colspan="9" class="text-center text-muted">加载中...</td></tr>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
                </div>
            `;
            loadPositionsData();
        }

        function loadConfigPage() {
            const page = document.getElementById('configPage');
            page.innerHTML = `
                <!-- 页面标题 -->
                <div class="row mb-3">
                    <div class="col-12">
                        <h3>⚙️ 配置管理</h3>
                        <p class="text-muted">所有配置修改后实时生效，无需重启程序</p>
                    </div>
                </div>

                <!-- 配置导航 -->
                <div class="row mb-4">
                    <div class="col-12">
                        <!-- 桌面端标签导航 -->
                        <ul class="nav nav-pills nav-fill d-none d-md-flex" id="configTabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="symbols-tab" data-bs-toggle="pill" href="#symbols-config" role="tab">
                                    📊 交易对配置
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="global-tab" data-bs-toggle="pill" href="#global-config" role="tab">
                                    🌐 全局配置
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="bark-tab" data-bs-toggle="pill" href="#bark-config" role="tab">
                                    🔔 通知配置
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="risk-tab" data-bs-toggle="pill" href="#risk-config" role="tab">
                                    🛡️ 风险控制
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="system-tab" data-bs-toggle="pill" href="#system-config" role="tab">
                                    🔧 系统设置
                                </a>
                            </li>
                        </ul>

                        <!-- 移动端下拉选择 -->
                        <select class="form-select d-md-none" id="mobileConfigSelect" onchange="switchConfigTab(this.value)">
                            <option value="symbols-config">📊 交易对配置</option>
                            <option value="global-config">🌐 全局配置</option>
                            <option value="bark-config">🔔 通知配置</option>
                            <option value="risk-config">🛡️ 风险控制</option>
                            <option value="system-config">🔧 系统设置</option>
                        </select>
                    </div>
                </div>

                <!-- 配置内容区域 -->
                <div class="tab-content" id="configTabContent">
                    <!-- 交易对配置 -->
                    <div class="tab-pane fade show active" id="symbols-config" role="tabpanel">
                        ${getSymbolsConfigHTML()}
                    </div>

                    <!-- 全局配置 -->
                    <div class="tab-pane fade" id="global-config" role="tabpanel">
                        ${getGlobalConfigHTML()}
                    </div>

                    <!-- 通知配置 -->
                    <div class="tab-pane fade" id="bark-config" role="tabpanel">
                        ${getBarkConfigHTML()}
                    </div>

                    <!-- 风险控制 -->
                    <div class="tab-pane fade" id="risk-config" role="tabpanel">
                        ${getRiskConfigHTML()}
                    </div>

                    <!-- 系统设置 -->
                    <div class="tab-pane fade" id="system-config" role="tabpanel">
                        ${getSystemConfigHTML()}
                    </div>
                </div>
            `;

            // 加载配置数据
            loadAllConfigData();
        }

        function loadAlertsPage() {
            const page = document.getElementById('alertsPage');
            page.innerHTML = `
                <div class="card">
                    <div class="card-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="bi bi-bell"></i> 警报历史</h5>
                            <button class="btn btn-outline-primary btn-sm" onclick="refreshAlerts()">
                                <i class="bi bi-arrow-clockwise"></i> 刷新
                            </button>
                        </div>
                    </div>
                    <div class="card-body">
                        <!-- 筛选条件 -->
                        <div class="row mb-3">
                            <div class="col-md-3 col-sm-6 mb-2">
                                <label class="form-label">交易对</label>
                                <select class="form-select form-select-sm" id="alertSymbolFilter">
                                    <option value="">全部交易对</option>
                                    <option value="BTCUSD">BTCUSD</option>
                                    <option value="ETHUSD">ETHUSD</option>
                                    <option value="SOLUSD">SOLUSD</option>
                                    <option value="XAUUSD">XAUUSD</option>
                                    <option value="GBPJPY">GBPJPY</option>
                                </select>
                            </div>
                            <div class="col-md-3 col-sm-6 mb-2">
                                <label class="form-label">信号方向</label>
                                <select class="form-select form-select-sm" id="alertDirectionFilter">
                                    <option value="">全部方向</option>
                                    <option value="BUY">买入</option>
                                    <option value="SELL">卖出</option>
                                </select>
                            </div>
                            <div class="col-md-3 col-sm-6 mb-2">
                                <label class="form-label">时间范围</label>
                                <select class="form-select form-select-sm" id="alertTimeFilter">
                                    <option value="24h">最近24小时</option>
                                    <option value="3d">最近3天</option>
                                    <option value="7d">最近7天</option>
                                    <option value="30d">最近30天</option>
                                    <option value="all">全部时间</option>
                                </select>
                            </div>
                            <div class="col-md-3 col-sm-6 mb-2">
                                <label class="form-label">每页显示</label>
                                <select class="form-select form-select-sm" id="alertPageSize">
                                    <option value="30">30条</option>
                                    <option value="50">50条</option>
                                    <option value="100">100条</option>
                                </select>
                            </div>
                        </div>

                        <!-- 筛选按钮 -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <button class="btn btn-primary btn-sm me-2" onclick="applyAlertsFilter()">
                                    <i class="bi bi-funnel"></i> 应用筛选
                                </button>
                                <button class="btn btn-outline-secondary btn-sm" onclick="resetAlertsFilter()">
                                    <i class="bi bi-arrow-clockwise"></i> 重置
                                </button>
                            </div>
                        </div>

                        <!-- 统计信息 -->
                        <div class="row mb-3">
                            <div class="col-12">
                                <div class="alert alert-info py-2 mb-0">
                                    <small id="alertsStatsInfo">正在加载统计信息...</small>
                                </div>
                            </div>
                        </div>

                        <!-- 表格 -->
                        <div class="table-responsive">
                            <table class="table table-hover">
                                <thead>
                                    <tr>
                                        <th>时间</th>
                                        <th>交易对</th>
                                        <th>信号方向</th>
                                        <th>价格</th>
                                        <th>强度</th>
                                        <th>处理结果</th>
                                        <th>操作</th>
                                    </tr>
                                </thead>
                                <tbody id="alertsTableBody">
                                    <tr><td colspan="7" class="text-center text-muted">加载中...</td></tr>
                                </tbody>
                            </table>
                        </div>

                        <!-- 分页 -->
                        <div class="d-flex justify-content-between align-items-center mt-3">
                            <div>
                                <small class="text-muted" id="alertsPaginationInfo">显示第 0-0 条，共 0 条记录</small>
                            </div>
                            <nav aria-label="警报历史分页">
                                <ul class="pagination pagination-sm mb-0" id="alertsPagination">
                                    <!-- 分页按钮将通过JavaScript生成 -->
                                </ul>
                            </nav>
                        </div>
                    </div>
                </div>

                <!-- 原始JSON数据模态框 -->
                <div class="modal fade" id="alertRawDataModal" tabindex="-1" aria-labelledby="alertRawDataModalLabel" aria-hidden="true">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title" id="alertRawDataModalLabel">
                                    <i class="bi bi-code-square"></i> 警报原始JSON数据
                                </h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                            </div>
                            <div class="modal-body">
                                <div class="mb-3">
                                    <label class="form-label">警报ID:</label>
                                    <span id="alertRawDataId" class="text-muted">-</span>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">原始JSON数据:</label>
                                    <div class="position-relative">
                                        <pre id="alertRawDataContent" class="bg-light p-3 rounded" style="max-height: 400px; overflow-y: auto; font-size: 0.85rem;"></pre>
                                        <button class="btn btn-outline-secondary btn-sm position-absolute top-0 end-0 m-2" onclick="copyAlertRawData()" title="复制JSON">
                                            <i class="bi bi-clipboard"></i>
                                        </button>
                                    </div>
                                </div>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                                <button type="button" class="btn btn-primary" onclick="copyAlertRawData()">
                                    <i class="bi bi-clipboard"></i> 复制JSON
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 初始化筛选器状态
            initAlertsFilters();

            // 加载数据
            loadAlertsData();
        }

        // 配置标签页切换函数
        function switchConfigTab(tabId) {
            // 隐藏所有标签页
            document.querySelectorAll('.tab-pane').forEach(pane => {
                pane.classList.remove('show', 'active');
            });

            // 显示目标标签页
            const targetPane = document.getElementById(tabId);
            if (targetPane) {
                targetPane.classList.add('show', 'active');
            }

            // 更新桌面端导航状态
            document.querySelectorAll('#configTabs .nav-link').forEach(link => {
                link.classList.remove('active');
            });

            const targetTab = document.querySelector(`#configTabs [href="#${tabId}"]`);
            if (targetTab) {
                targetTab.classList.add('active');
            }
        }

        // 配置数据加载函数
        async function loadAllConfigData() {
            try {
                // 加载交易对配置
                await loadSymbolConfigs();

                // 加载全局配置
                await loadGlobalConfig();

                // 加载通知配置
                await loadBarkConfig();

                // 加载风险控制配置
                await loadRiskConfig();

                // 加载系统配置
                await loadSystemConfig();

                // 加载UI配置
                await loadUIConfig();

                // 加载MT5配置
                await loadMT5Config();

            } catch (error) {
                console.error('加载配置数据失败:', error);
                ui.showAlert('danger', '加载配置数据失败: ' + error.message);
            }
        }

        async function loadMT5Config() {
            try {
                const response = await ui.apiCall('/api/config/mt5');
                if (response && response.success) {
                    const config = response.data;

                    // 填充MT5配置表单
                    const mt5LoginElement = document.getElementById('mt5Login');
                    const mt5PasswordElement = document.getElementById('mt5Password');
                    const mt5ServerElement = document.getElementById('mt5Server');

                    if (mt5LoginElement && config.login) {
                        mt5LoginElement.value = config.login;
                    }
                    if (mt5PasswordElement && config.password) {
                        mt5PasswordElement.value = config.password;
                    }
                    if (mt5ServerElement && config.server) {
                        mt5ServerElement.value = config.server;
                    }

                    console.log('MT5配置加载成功:', config);
                } else {
                    console.log('MT5配置加载失败或无配置');
                }
            } catch (error) {
                console.error('加载MT5配置失败:', error);
            }
        }

        async function loadGlobalConfig() {
            try {
                const response = await ui.apiCall('/api/config/global-sl-tp');
                if (response && response.success) {
                    const config = response.data;

                    // 填充全局配置表单
                    const fields = [
                        'globalTradingCooldown', 'globalMaxConcurrentTrades',
                        'globalStopLossUSD', 'globalTakeProfitUSD',
                        'globalMaxDailyLoss', 'globalMaxDailyProfit', 'globalMaxDailyTrades'
                    ];

                    fields.forEach(field => {
                        const element = document.getElementById(field);
                        if (element && config[field.replace('global', '').toLowerCase()]) {
                            element.value = config[field.replace('global', '').toLowerCase()];
                        }
                    });

                    // 填充复选框
                    const checkboxes = ['globalAutoTradingEnabled', 'globalEmergencyStopEnabled'];
                    checkboxes.forEach(checkbox => {
                        const element = document.getElementById(checkbox);
                        if (element && config[checkbox.replace('global', '').toLowerCase()] !== undefined) {
                            element.checked = config[checkbox.replace('global', '').toLowerCase()];
                        }
                    });
                }
            } catch (error) {
                console.error('加载全局配置失败:', error);
            }
        }

        async function loadBarkConfig() {
            try {
                // 从Bark配置API获取完整配置
                const response = await ui.apiCall('/api/config/bark');
                if (response && response.success) {
                    const config = response.data;
                    console.log('加载的Bark配置:', config);

                    // 设置启用状态
                    const barkEnabled = document.getElementById('barkEnabled');
                    if (barkEnabled) {
                        barkEnabled.checked = config.enabled === true;
                    }

                    // 设置设备Key
                    const deviceKeys = config.device_keys || [];
                    const barkDeviceKey1 = document.getElementById('barkDeviceKey1');
                    if (barkDeviceKey1) {
                        barkDeviceKey1.value = deviceKeys[0] || '';
                    }

                    const barkDeviceKey2 = document.getElementById('barkDeviceKey2');
                    if (barkDeviceKey2) {
                        barkDeviceKey2.value = deviceKeys[1] || '';
                    }

                    // 设置服务器地址
                    const barkServerUrl = document.getElementById('barkServerUrl');
                    if (barkServerUrl) {
                        barkServerUrl.value = config.server_url || 'https://api.day.app';
                    }

                    // 设置声音
                    const barkSound = document.getElementById('barkSound');
                    if (barkSound) {
                        barkSound.value = config.sound || 'birdsong';
                    }

                    // 设置通知类型
                    const notificationSettings = {
                        'notifyTradeOpen': config.trade_open !== false,
                        'notifyTradeClose': config.trade_close !== false,
                        'notifyAlertForward': config.alert_forward === true,
                        'notifySystemStatus': config.system_status !== false,
                        'notifyBalanceReport': config.balance_report === true
                    };

                    Object.entries(notificationSettings).forEach(([field, value]) => {
                        const element = document.getElementById(field);
                        if (element) {
                            element.checked = value;
                        }
                    });

                    // 设置余额报告间隔
                    const balanceReportInterval = document.getElementById('balanceReportInterval');
                    if (balanceReportInterval) {
                        balanceReportInterval.value = config.balance_report_interval || '12';
                    }

                    console.log('Bark配置加载完成');
                } else {
                    console.warn('获取Bark配置失败，使用默认值');
                    // 设置默认值
                    setDefaultBarkConfig();
                }
            } catch (error) {
                console.error('加载Bark配置失败:', error);
                setDefaultBarkConfig();
            }
        }

        function setDefaultBarkConfig() {
            // 设置默认值
            const barkServerUrl = document.getElementById('barkServerUrl');
            if (barkServerUrl) {
                barkServerUrl.value = 'https://api.day.app';
            }

            const barkSound = document.getElementById('barkSound');
            if (barkSound) {
                barkSound.value = 'birdsong';
            }

            // 设置默认通知设置
            const defaultNotifications = {
                'notifyTradeOpen': true,
                'notifyTradeClose': true,
                'notifyAlertForward': false,
                'notifySystemStatus': true,
                'notifyBalanceReport': false
            };

            Object.entries(defaultNotifications).forEach(([field, defaultValue]) => {
                const element = document.getElementById(field);
                if (element) {
                    element.checked = defaultValue;
                }
            });

            const balanceReportInterval = document.getElementById('balanceReportInterval');
            if (balanceReportInterval) {
                balanceReportInterval.value = '12';
            }
        }

        async function loadRiskConfig() {
            try {
                // 直接从风险配置API获取设置
                const response = await fetch('/api/config/risk');
                if (response.ok) {
                    const data = await response.json();
                    if (data && data.success) {
                        const config = data.data;

                        // 设置风险控制参数
                        const riskConfigMapping = {
                            'riskCheckInterval': config.risk_check_interval || 30,
                            'balanceRecordInterval': config.balance_record_interval || 5,
                            'maxDrawdownPercent': config.max_drawdown_percent || 10,
                            'maxRiskPerTrade': config.max_risk_per_trade || 2,
                            'maxConsecutiveLosses': config.max_consecutive_losses || 5,
                            'tradingPauseDuration': config.trading_pause_duration || 30
                        };

                        Object.entries(riskConfigMapping).forEach(([field, value]) => {
                            const element = document.getElementById(field);
                            if (element) {
                                element.value = value;
                            }
                        });

                        // 设置风险控制复选框
                        const riskCheckboxMapping = {
                            'enableRiskControl': config.enable_risk_control !== false,
                            'enableEmergencyStop': config.enable_emergency_stop !== false,
                            'enableAutoRecovery': config.enable_auto_recovery === true
                        };

                        Object.entries(riskCheckboxMapping).forEach(([checkbox, value]) => {
                            const element = document.getElementById(checkbox);
                            if (element) {
                                element.checked = value;
                            }
                        });
                    }
                } else {
                    console.error('加载风险配置失败:', response.status);
                }
            } catch (error) {
                console.error('加载风险配置失败:', error);
            }
        }

        async function loadSystemConfig() {
            try {
                const response = await ui.apiCall('/api/config/system');
                if (response && response.success) {
                    const config = response.data;

                    // 填充系统配置表单（移除UI相关字段）
                    const systemFields = [
                        'webhookPort', 'webPort', 'logLevel', 'backupInterval'
                    ];

                    systemFields.forEach(field => {
                        const element = document.getElementById(field);
                        if (element && config[field] !== undefined) {
                            element.value = config[field];
                        }
                    });
                }
            } catch (error) {
                console.error('加载系统配置失败:', error);
            }
        }

        async function loadUIConfig() {
            try {
                const response = await ui.apiCall('/api/config/ui');
                if (response && response.success) {
                    const config = response.data;

                    // 填充UI配置表单（字段名映射）
                    const fieldMapping = {
                        'uiTheme': 'ui_theme',
                        'refreshInterval': 'refresh_interval'
                    };

                    Object.entries(fieldMapping).forEach(([elementId, configKey]) => {
                        const element = document.getElementById(elementId);
                        if (element && config[configKey] !== undefined) {
                            element.value = config[configKey];
                        }
                    });

                    // 填充UI复选框（字段名映射）
                    const checkboxMapping = {
                        'soundEnabled': 'sound_enabled',
                        'mobileOptimized': 'mobile_optimized'
                    };

                    Object.entries(checkboxMapping).forEach(([elementId, configKey]) => {
                        const element = document.getElementById(elementId);
                        if (element && config[configKey] !== undefined) {
                            element.checked = config[configKey];
                        }
                    });
                }
            } catch (error) {
                console.error('加载UI配置失败:', error);
            }
        }

        // 交易记录相关变量
        let currentTradesPage = 1;
        let tradesFilters = {
            status: '',
            symbol: '',
            limit: 20
        };

        // 数据加载函数
        async function loadTradesData() {
            try {
                const params = new URLSearchParams({
                    page: currentTradesPage,
                    limit: tradesFilters.limit,
                    status: tradesFilters.status,
                    symbol: tradesFilters.symbol
                });

                // 直接使用fetch，不依赖ui.apiCall的认证
                const response = await fetch(`/api/trades?${params}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data && data.success && data.data) {
                        const trades = data.data.trades || [];
                        const pagination = {
                            page: data.data.page || 1,
                            total: data.data.total || 0,
                            limit: data.data.limit || 20,
                            pages: Math.ceil((data.data.total || 0) / (data.data.limit || 20))
                        };
                        renderTradesTable(trades);
                        renderTradesPagination(pagination);
                    } else {
                        console.error('API返回错误:', data);
                        const tbody = document.getElementById('tradesTableBody');
                        if (tbody) {
                            tbody.innerHTML = '<tr><td colspan="14" class="text-center text-warning">暂无交易记录</td></tr>';
                        }
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                console.error('加载交易记录失败:', error);
                const tbody = document.getElementById('tradesTableBody');
                if (tbody) {
                    tbody.innerHTML = '<tr><td colspan="14" class="text-center text-danger">加载失败，请检查网络连接</td></tr>';
                }
            }
        }

        async function loadPositionsData() {
            try {
                // 同时获取持仓数据和账户信息
                const [positionsResponse, accountResponse] = await Promise.all([
                    ui.apiCall('/api/positions'),
                    ui.apiCall('/api/account/info')
                ]);

                if (positionsResponse && positionsResponse.success) {
                    const positions = positionsResponse.data || [];
                    renderPositionsTable(positions);
                    updatePositionStatistics(positions);
                }

                if (accountResponse && accountResponse.success) {
                    updateAccountInfo(accountResponse.data);
                }
            } catch (error) {
                console.error('加载持仓数据失败:', error);
            }
        }

        function updateAccountInfo(accountData) {
            // 更新账户基本信息
            document.getElementById('accountBalanceDetail').textContent =
                accountData.balance ? `$${parseFloat(accountData.balance).toFixed(2)}` : '$0.00';
            document.getElementById('accountEquityDetail').textContent =
                accountData.equity ? `$${parseFloat(accountData.equity).toFixed(2)}` : '$0.00';
            document.getElementById('accountMargin').textContent =
                accountData.margin ? `$${parseFloat(accountData.margin).toFixed(2)}` : '$0.00';
        }

        function updatePositionStatistics(positions) {
            console.log('更新持仓统计，持仓数据:', positions);

            let totalPositions = positions.length;
            let profitableCount = 0;
            let lossCount = 0;
            let totalVolume = 0;
            let buyCount = 0;
            let sellCount = 0;
            let totalFloatingPL = 0;

            positions.forEach((position, index) => {
                console.log(`处理持仓 ${index + 1}:`, position);

                // 计算盈亏
                const profit = parseFloat(position.profit || 0);
                totalFloatingPL += profit;

                if (profit > 0) {
                    profitableCount++;
                } else if (profit < 0) {
                    lossCount++;
                }

                // 计算手数
                const volume = parseFloat(position.volume || 0);
                totalVolume += volume;
                console.log(`手数: ${volume}, 累计: ${totalVolume}`);

                // 计算买卖方向 - 检查多个可能的字段
                const type = position.type_str || position.operation || position.type;
                console.log(`交易方向字段: type_str=${position.type_str}, operation=${position.operation}, type=${position.type}, 最终使用: ${type}`);

                if (type === 'BUY' || type === '做多' || type === 'buy' || type === 0) {
                    buyCount++;
                    console.log('识别为买入/做多');
                } else if (type === 'SELL' || type === '做空' || type === 'sell' || type === 1) {
                    sellCount++;
                    console.log('识别为卖出/做空');
                } else {
                    console.warn('未识别的交易方向:', type);
                }
            });

            console.log('统计结果:', {
                totalPositions,
                profitableCount,
                lossCount,
                totalVolume,
                buyCount,
                sellCount,
                totalFloatingPL
            });

            // 更新统计信息显示
            document.getElementById('totalPositions').textContent = totalPositions;
            document.getElementById('profitablePositions').textContent = profitableCount;
            document.getElementById('lossPositions').textContent = lossCount;
            document.getElementById('totalVolume').textContent = totalVolume.toFixed(2);
            document.getElementById('buyPositions').textContent = buyCount;
            document.getElementById('sellPositions').textContent = sellCount;

            // 更新浮动盈亏，根据盈亏情况设置颜色
            const floatingPLElement = document.getElementById('totalFloatingPL');
            floatingPLElement.textContent = `$${totalFloatingPL.toFixed(2)}`;

            // 动态调整浮动盈亏卡片颜色
            const floatingPLCard = floatingPLElement.closest('.card');
            floatingPLCard.className = 'card text-white ' +
                (totalFloatingPL > 0 ? 'bg-success' :
                 totalFloatingPL < 0 ? 'bg-danger' : 'bg-secondary');
        }

        async function loadSymbolConfigs() {
            try {
                const response = await ui.apiCall('/api/config/symbols');
                if (response && response.success) {
                    renderSymbolConfigs(response.data || {});
                }
            } catch (error) {
                console.error('加载交易对配置失败:', error);
            }
        }

        // 警报筛选状态
        let alertsCurrentPage = 1;
        let alertsCurrentFilters = {
            symbol: '',
            direction: '',
            timeRange: '24h',
            pageSize: 30
        };

        function initAlertsFilters() {
            // 设置默认值
            document.getElementById('alertTimeFilter').value = '24h';
            document.getElementById('alertPageSize').value = '30';

            // 绑定筛选器变化事件
            ['alertSymbolFilter', 'alertDirectionFilter', 'alertTimeFilter', 'alertPageSize'].forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    element.addEventListener('change', () => {
                        alertsCurrentPage = 1; // 重置到第一页
                        applyAlertsFilter();
                    });
                }
            });
        }

        function applyAlertsFilter() {
            // 获取筛选条件
            alertsCurrentFilters = {
                symbol: document.getElementById('alertSymbolFilter').value,
                direction: document.getElementById('alertDirectionFilter').value,
                timeRange: document.getElementById('alertTimeFilter').value,
                pageSize: parseInt(document.getElementById('alertPageSize').value)
            };

            // 重置到第一页
            alertsCurrentPage = 1;

            // 加载数据
            loadAlertsData();
        }

        function resetAlertsFilter() {
            // 重置筛选器
            document.getElementById('alertSymbolFilter').value = '';
            document.getElementById('alertDirectionFilter').value = '';
            document.getElementById('alertTimeFilter').value = '24h';
            document.getElementById('alertPageSize').value = '30';

            // 应用筛选
            applyAlertsFilter();
        }

        function refreshAlerts() {
            loadAlertsData();
        }

        async function loadAlertsData() {
            try {
                // 构建查询参数
                const params = new URLSearchParams({
                    page: alertsCurrentPage,
                    limit: alertsCurrentFilters.pageSize
                });

                // 添加筛选条件
                if (alertsCurrentFilters.symbol) {
                    params.append('symbol', alertsCurrentFilters.symbol);
                }
                if (alertsCurrentFilters.direction) {
                    params.append('signal_type', alertsCurrentFilters.direction);
                }
                if (alertsCurrentFilters.timeRange && alertsCurrentFilters.timeRange !== 'all') {
                    params.append('time_range', alertsCurrentFilters.timeRange);
                }

                const response = await ui.apiCall(`/api/alerts?${params.toString()}`);
                console.log('警报API响应:', response);

                if (response && response.success && response.data) {
                    const alerts = response.data.alerts || [];
                    const total = response.data.total || 0;
                    const page = response.data.page || 1;
                    const limit = response.data.limit || alertsCurrentFilters.pageSize;

                    // 渲染表格
                    renderAlertsTable(alerts);

                    // 更新分页
                    updateAlertsPagination(total, page, limit);

                    // 更新统计信息
                    updateAlertsStats(total, alerts.length, alertsCurrentFilters);
                } else {
                    console.log('API调用失败或无数据:', response);
                    renderAlertsTable([]);
                    updateAlertsPagination(0, 1, alertsCurrentFilters.pageSize);
                    updateAlertsStats(0, 0, alertsCurrentFilters);
                }
            } catch (error) {
                console.error('加载警报数据失败:', error);
                renderAlertsTable([]);
                updateAlertsPagination(0, 1, alertsCurrentFilters.pageSize);
                updateAlertsStats(0, 0, alertsCurrentFilters);
            }
        }

        // 渲染函数
        function renderTradesTable(trades) {
            const tbody = document.getElementById('tradesTableBody');
            if (!tbody) return;

            if (trades.length === 0) {
                tbody.innerHTML = '<tr><td colspan="14" class="text-center text-muted">暂无交易记录</td></tr>';
                return;
            }

            tbody.innerHTML = trades.map(trade => `
                <tr>
                    <td><strong>${trade.mt5_ticket || trade.ticket || '-'}</strong></td>
                    <td>${trade.symbol || '-'}</td>
                    <td><span class="badge bg-${trade.direction === 'BUY' ? 'success' : 'danger'}">${trade.direction || '-'}</span></td>
                    <td>${trade.lot_size || '-'}</td>
                    <td>${trade.open_price || '-'}</td>
                    <td>${trade.close_price || '-'}</td>
                    <td><span class="badge bg-${trade.status === 'OPEN' ? 'primary' : 'secondary'}">${trade.status || '-'}</span></td>
                    <td class="${(trade.profit_usd || trade.profit || 0) >= 0 ? 'text-success' : 'text-danger'}">
                        $${(trade.profit_usd || trade.profit || 0).toFixed(2)}
                    </td>
                    <td>${formatToBeijingTime(trade.open_time)}</td>
                    <td>${formatToBeijingTime(trade.close_time)}</td>
                    <td><small>${trade.open_reason || '-'}</small></td>
                    <td><small>${trade.close_reason || '-'}</small></td>
                    <td>${getSyncStatusBadge(trade.last_sync_time)}</td>
                    <td>
                        <button class="btn btn-outline-warning btn-sm"
                                onclick="syncSingleOrder(${trade.mt5_ticket || trade.ticket})"
                                ${!(trade.mt5_ticket || trade.ticket) ? 'disabled' : ''}>
                            <i class="bi bi-arrow-repeat"></i>
                        </button>
                    </td>
                </tr>
            `).join('');
        }

        function renderPositionsTable(positions) {
            console.log('渲染持仓表格，持仓数量:', positions.length);
            console.log('持仓数据:', positions);

            // 生成表格行HTML的函数
            function generateTableRows(includeTicket = false, includeSlTp = true, includeMonitoring = false) {
                if (positions.length === 0) {
                    let colSpan = 7; // 基础列数
                    if (includeTicket) colSpan++; // 订单号列
                    if (includeSlTp) colSpan++; // 止盈止损列
                    if (includeMonitoring) colSpan++; // 监控倒计时列
                    return `<tr><td colspan="${colSpan}" class="text-center text-muted">暂无持仓</td></tr>`;
                }

                return positions.map(pos => {
                    // 确保数据类型正确
                    const profit = parseFloat(pos.profit) || 0;
                    const volume = parseFloat(pos.volume) || 0;
                    const priceOpen = parseFloat(pos.price_open) || 0;
                    const priceCurrent = parseFloat(pos.price_current) || 0;

                    // 判断交易方向
                    let direction = 'BUY';
                    let directionClass = 'success';
                    if (pos.type === 1 || pos.type_str === 'SELL') {
                        direction = 'SELL';
                        directionClass = 'danger';
                    }

                    const ticketColumn = includeTicket ? `<td>${pos.ticket || '-'}</td>` : '';

                    // 操作列增加保护止盈按钮
                    const actionButtons = `
                        <div class="btn-group btn-group-sm">
                            <button class="btn btn-outline-primary btn-sm" onclick="showSlTpModal(${pos.ticket}, '${pos.symbol}', ${profit})" title="设置止盈止损">
                                <i class="bi bi-gear"></i>
                            </button>
                            <button class="btn btn-outline-warning btn-sm" onclick="showProtectiveProfitModal(${pos.ticket}, ${profit})" title="保护止盈" ${profit <= 0 ? 'disabled' : ''}>
                                <i class="bi bi-shield-check"></i>
                            </button>
                            <button class="btn btn-danger btn-sm" onclick="closePosition(${pos.ticket})" title="平仓订单#${pos.ticket}">
                                <i class="bi bi-x"></i> 平仓
                            </button>
                        </div>
                    `;

                    // 止盈止损列
                    const slTpColumn = includeSlTp ? `
                        <td>
                            ${actionButtons}
                        </td>
                    ` : '';

                    // 监控倒计时列
                    const monitoringColumn = includeMonitoring ? `
                        <td>
                            <div class="d-flex align-items-center">
                                <span id="countdown-${pos.ticket}" class="badge bg-secondary me-2">检查中...</span>
                                <div class="btn-group btn-group-sm">
                                    <button id="add-monitor-btn-${pos.ticket}" class="btn btn-outline-success btn-sm" onclick="addPositionMonitoring(${pos.ticket})" title="添加超时监控" style="display: none;">
                                        <i class="bi bi-shield-plus"></i>
                                    </button>
                                    <button id="remove-monitor-btn-${pos.ticket}" class="btn btn-outline-danger btn-sm" onclick="removePositionMonitoring(${pos.ticket})" title="移除超时监控" style="display: none;">
                                        <i class="bi bi-shield-x"></i>
                                    </button>
                                </div>
                            </div>
                        </td>
                    ` : '';

                    return `
                        <tr>
                            ${ticketColumn}
                            <td>${pos.symbol || '-'}</td>
                            <td><span class="badge bg-${directionClass}">${direction}</span></td>
                            <td>${volume.toFixed(2)}</td>
                            <td>${priceOpen.toFixed(5)}</td>
                            <td>${priceCurrent.toFixed(5)}</td>
                            <td class="${profit >= 0 ? 'text-success' : 'text-danger'}">$${profit.toFixed(2)}</td>
                            ${slTpColumn}
                            ${monitoringColumn}
                        </tr>
                    `;
                }).join('');
            }

            // 更新主要的持仓表格（首页包含止盈止损设置）
            const tbody = document.getElementById('positionsTableBody');
            if (tbody) {
                tbody.innerHTML = generateTableRows(false, true);
                console.log('已更新positionsTableBody');
            }

            // 更新详细的持仓表格（持仓管理页面，包含订单号、止盈止损和监控）
            const detailTbody = document.getElementById('positionsDetailTableBody');
            if (detailTbody) {
                detailTbody.innerHTML = generateTableRows(true, true, true);
                console.log('已更新positionsDetailTableBody');

                // 加载监控信息
                loadMonitoringInfo();
            }

            console.log('持仓表格渲染完成');

            // 移动端表格滚动优化 - 在表格内容更新后立即执行
            setTimeout(() => {
                if (window.innerWidth <= 768) {
                    optimizeMobileTableScroll();
                }
            }, 100);
        }

        // 移动端表格滚动优化函数 - 简化版本
        function optimizeMobileTableScroll() {
            if (window.innerWidth > 768) return;

            console.log('执行移动端表格滚动优化...');

            // 查找所有表格容器并强制应用样式
            document.querySelectorAll('.table-responsive').forEach(container => {
                // 强制容器可滚动
                container.style.overflowX = 'auto';
                container.style.overflowY = 'hidden';
                container.style.webkitOverflowScrolling = 'touch';
                container.style.width = '100%';
                container.style.display = 'block';

                const table = container.querySelector('table');
                if (table) {
                    // 强制表格最小宽度
                    table.style.minWidth = '800px';
                    table.style.width = 'auto';

                    // 强制显示所有隐藏的列
                    table.querySelectorAll('.d-none').forEach(cell => {
                        if (cell.classList.contains('d-md-table-cell') ||
                            cell.classList.contains('d-lg-table-cell') ||
                            cell.classList.contains('d-sm-table-cell')) {
                            cell.style.display = 'table-cell';
                        }
                    });

                    // 优化操作列
                    table.querySelectorAll('th:last-child, td:last-child').forEach(cell => {
                        cell.style.position = 'sticky';
                        cell.style.right = '0';
                        cell.style.backgroundColor = 'white';
                        cell.style.zIndex = '10';
                        cell.style.borderLeft = '1px solid #dee2e6';
                        cell.style.minWidth = '80px';
                        cell.style.maxWidth = '90px';
                        cell.style.width = '85px';
                    });

                    // 优化表头
                    table.querySelectorAll('thead th').forEach(cell => {
                        cell.style.position = 'sticky';
                        cell.style.top = '0';
                        cell.style.backgroundColor = '#f8f9fa';
                        cell.style.zIndex = '5';
                    });

                    // 操作列表头特殊处理
                    const lastHeader = table.querySelector('thead th:last-child');
                    if (lastHeader) {
                        lastHeader.style.zIndex = '15';
                    }
                }
            });

            console.log('移动端表格优化完成');
        }

        function renderSymbolConfigs(configs) {
            const tbody = document.getElementById('symbolsConfigBody');
            if (!tbody) return;

            // 完整的交易对列表，按分类组织
            const symbolCategories = {
                'precious_metals': {
                    name: '贵金属',
                    symbols: ['XAUUSD', 'XAGUSD']
                },
                'crypto': {
                    name: '加密货币',
                    symbols: ['BTCUSD', 'ETHUSD', 'SOLUSD', 'ADAUSD', 'XLMUSD', 'DOGEUSD', 'LINKUSD', 'LTCUSD', 'XRPUSD']
                },
                'forex': {
                    name: '外汇',
                    symbols: ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD', 'USDCAD', 'USDCHF', 'NZDUSD', 'GBPJPY', 'EURJPY']
                }
            };

            let html = '';

            Object.entries(symbolCategories).forEach(([categoryKey, category]) => {
                // 添加分类标题行
                html += `
                    <tr class="table-secondary">
                        <td colspan="8" class="fw-bold">
                            <i class="bi bi-folder"></i> ${category.name}
                        </td>
                    </tr>
                `;

                // 添加该分类下的交易对
                category.symbols.forEach(symbol => {
                    const config = configs[symbol] || {
                        enabled: false,
                        lot_size: 0.01,
                        stop_loss_usd: 50,
                        take_profit_usd: 100,
                        signal_timeout_seconds: 180
                    };

                    html += `
                        <tr data-symbol="${symbol}" data-category="${categoryKey}">
                            <td class="d-none d-md-table-cell">
                                <div class="form-check">
                                    <input class="form-check-input symbol-enabled" type="checkbox"
                                           ${config.enabled ? 'checked' : ''}
                                           onchange="updateSymbolConfig('${symbol}', 'enabled', this.checked)">
                                </div>
                            </td>
                            <td>
                                <div class="d-flex align-items-center">
                                    <strong>${symbol}</strong>
                                    <div class="d-md-none ms-2">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox"
                                                   ${config.enabled ? 'checked' : ''}
                                                   onchange="updateSymbolConfig('${symbol}', 'enabled', this.checked)">
                                        </div>
                                    </div>
                                </div>
                            </td>
                            <td class="d-none d-lg-table-cell">
                                <span class="badge bg-secondary">${category.name}</span>
                            </td>
                            <td>
                                <input type="number" class="form-control form-control-sm"
                                       value="${config.lot_size}" step="0.01" min="0.01"
                                       style="width: 80px;"
                                       onchange="updateSymbolConfig('${symbol}', 'lot_size', parseFloat(this.value))">
                            </td>
                            <td>
                                <input type="number" class="form-control form-control-sm"
                                       value="${config.stop_loss_usd}" step="1" min="1"
                                       style="width: 80px;"
                                       onchange="updateSymbolConfig('${symbol}', 'stop_loss_usd', parseInt(this.value))">
                            </td>
                            <td>
                                <input type="number" class="form-control form-control-sm"
                                       value="${config.take_profit_usd}" step="1" min="1"
                                       style="width: 80px;"
                                       onchange="updateSymbolConfig('${symbol}', 'take_profit_usd', parseInt(this.value))">
                            </td>
                            <td class="d-none d-md-table-cell">
                                <input type="number" class="form-control form-control-sm"
                                       value="${config.signal_timeout_seconds}" step="30" min="30"
                                       style="width: 80px;"
                                       onchange="updateSymbolConfig('${symbol}', 'signal_timeout_seconds', parseInt(this.value))">
                            </td>
                            <td>
                                <div class="btn-group btn-group-sm">
                                    <button class="btn btn-outline-primary btn-sm"
                                            onclick="editSymbolConfig('${symbol}')"
                                            title="编辑">
                                        <i class="bi bi-pencil"></i>
                                    </button>
                                    <button class="btn btn-outline-danger btn-sm"
                                            onclick="resetSymbolConfig('${symbol}')"
                                            title="重置">
                                        <i class="bi bi-arrow-clockwise"></i>
                                    </button>
                                </div>
                            </td>
                        </tr>
                    `;
                });
            });

            tbody.innerHTML = html;

            // 移动端表格滚动优化 - 在配置表格内容更新后立即执行
            setTimeout(() => {
                if (window.innerWidth <= 768) {
                    optimizeMobileTableScroll();
                }
            }, 100);
        }

        function updateAlertsStats(total, currentCount, filters) {
            const statsElement = document.getElementById('alertsStatsInfo');
            if (!statsElement) return;

            let filterText = '';
            const activeFilters = [];

            if (filters.symbol) activeFilters.push(`交易对: ${filters.symbol}`);
            if (filters.direction) activeFilters.push(`方向: ${filters.direction === 'BUY' ? '买入' : '卖出'}`);
            if (filters.timeRange !== 'all') {
                const timeRangeText = {
                    '24h': '最近24小时',
                    '3d': '最近3天',
                    '7d': '最近7天',
                    '30d': '最近30天'
                };
                activeFilters.push(`时间: ${timeRangeText[filters.timeRange] || filters.timeRange}`);
            }

            if (activeFilters.length > 0) {
                filterText = ` (筛选条件: ${activeFilters.join(', ')})`;
            }

            statsElement.innerHTML = `
                <i class="bi bi-info-circle"></i>
                共找到 <strong>${total}</strong> 条警报记录，当前显示 <strong>${currentCount}</strong> 条${filterText}
            `;
        }

        function updateAlertsPagination(total, currentPage, pageSize) {
            const totalPages = Math.ceil(total / pageSize);
            const paginationElement = document.getElementById('alertsPagination');
            const paginationInfoElement = document.getElementById('alertsPaginationInfo');

            if (!paginationElement || !paginationInfoElement) return;

            // 更新分页信息
            const startIndex = (currentPage - 1) * pageSize + 1;
            const endIndex = Math.min(currentPage * pageSize, total);
            paginationInfoElement.textContent = `显示第 ${total > 0 ? startIndex : 0}-${endIndex} 条，共 ${total} 条记录`;

            // 生成分页按钮
            let paginationHTML = '';

            if (totalPages <= 1) {
                paginationElement.innerHTML = '';
                return;
            }

            // 上一页按钮
            paginationHTML += `
                <li class="page-item ${currentPage <= 1 ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="goToAlertsPage(${currentPage - 1})" aria-label="上一页">
                        <span aria-hidden="true">&laquo;</span>
                    </a>
                </li>
            `;

            // 页码按钮
            const maxVisiblePages = 5;
            let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
            let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

            if (endPage - startPage + 1 < maxVisiblePages) {
                startPage = Math.max(1, endPage - maxVisiblePages + 1);
            }

            for (let i = startPage; i <= endPage; i++) {
                paginationHTML += `
                    <li class="page-item ${i === currentPage ? 'active' : ''}">
                        <a class="page-link" href="#" onclick="goToAlertsPage(${i})">${i}</a>
                    </li>
                `;
            }

            // 下一页按钮
            paginationHTML += `
                <li class="page-item ${currentPage >= totalPages ? 'disabled' : ''}">
                    <a class="page-link" href="#" onclick="goToAlertsPage(${currentPage + 1})" aria-label="下一页">
                        <span aria-hidden="true">&raquo;</span>
                    </a>
                </li>
            `;

            paginationElement.innerHTML = paginationHTML;
        }

        function goToAlertsPage(page) {
            if (page < 1) return;
            alertsCurrentPage = page;
            loadAlertsData();
        }

        // 时间格式化函数 - 转换为北京时间
        function formatToBeijingTime(dateString) {
            if (!dateString) return '-';

            try {
                // 创建Date对象
                const date = new Date(dateString);

                // 检查日期是否有效
                if (isNaN(date.getTime())) {
                    return dateString; // 如果无法解析，返回原始字符串
                }

                // 转换为北京时间 (UTC+8)
                const beijingTime = new Date(date.getTime() + (8 * 60 * 60 * 1000));

                // 格式化为 YYYY-MM-DD HH:mm:ss
                const year = beijingTime.getUTCFullYear();
                const month = String(beijingTime.getUTCMonth() + 1).padStart(2, '0');
                const day = String(beijingTime.getUTCDate()).padStart(2, '0');
                const hours = String(beijingTime.getUTCHours()).padStart(2, '0');
                const minutes = String(beijingTime.getUTCMinutes()).padStart(2, '0');
                const seconds = String(beijingTime.getUTCSeconds()).padStart(2, '0');

                return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
            } catch (error) {
                console.error('时间格式化失败:', error, '原始时间:', dateString);
                return dateString;
            }
        }

        function renderAlertsTable(alerts) {
            const tbody = document.getElementById('alertsTableBody');
            if (!tbody) {
                console.error('找不到alertsTableBody元素');
                return;
            }

            console.log('渲染警报表格，数据数量:', alerts.length);

            if (alerts.length === 0) {
                tbody.innerHTML = '<tr><td colspan="7" class="text-center text-muted">暂无警报记录</td></tr>';
                return;
            }

            tbody.innerHTML = alerts.map(alert => `
                <tr>
                    <td>${formatToBeijingTime(alert.timestamp || alert.created_at)}</td>
                    <td>${alert.standard_symbol || alert.symbol || '-'}</td>
                    <td><span class="badge bg-${(alert.standard_signal || alert.signal_direction) === 'BUY' ? 'success' : 'danger'}">${alert.standard_signal || alert.signal_direction || '-'}</span></td>
                    <td>${alert.price || '-'}</td>
                    <td>${alert.signal_strength || '-'}</td>
                    <td>${getProcessResultBadge(alert)}</td>
                    <td>
                        <button class="btn btn-outline-info btn-sm" onclick="viewAlertRawData('${alert.alert_id}')" title="查看原始JSON">
                            <i class="bi bi-code-square"></i>
                        </button>
                    </td>
                </tr>
            `).join('');

            // 移动端表格滚动优化
            setTimeout(() => {
                if (window.innerWidth <= 768) {
                    optimizeMobileTableScroll();
                }
            }, 100);
        }

        // 获取处理结果徽章
        function getProcessResultBadge(alert) {
            if (!alert.processed) {
                return '<span class="badge bg-warning">待处理</span>';
            }

            const resultType = alert.process_result;
            const reason = alert.process_reason || '';

            let badgeClass = 'bg-secondary';
            let displayText = '已处理';

            switch (resultType) {
                case '下多单':
                    badgeClass = 'bg-success';
                    displayText = '下多单';
                    break;
                case '下空单':
                    badgeClass = 'bg-danger';
                    displayText = '下空单';
                    break;
                case '维持买入':
                    badgeClass = 'bg-info';
                    displayText = '维持买入';
                    break;
                case '维持做空':
                    badgeClass = 'bg-info';
                    displayText = '维持做空';
                    break;
                case '冷却未下单':
                    badgeClass = 'bg-warning';
                    displayText = '冷却未下单';
                    break;
                default:
                    if (alert.processed) {
                        badgeClass = 'bg-success';
                        displayText = '已处理';
                    }
                    break;
            }

            const title = reason ? `title="${reason}"` : '';
            return `<span class="badge ${badgeClass}" ${title}>${displayText}</span>`;
        }

        // 配置HTML生成函数
        function getSymbolsConfigHTML() {
            return `
                <div class="row">
                    <div class="col-lg-8 col-md-12 mb-3">
                        <div class="card">
                            <div class="card-header d-flex flex-column flex-md-row justify-content-between align-items-start align-items-md-center">
                                <h5 class="mb-2 mb-md-0">📊 交易对配置</h5>
                                <div class="btn-group btn-group-sm w-100 w-md-auto">
                                    <button class="btn btn-success" onclick="enableAllSymbols()">全部启用</button>
                                    <button class="btn btn-warning" onclick="disableAllSymbols()">全部禁用</button>
                                    <button class="btn btn-info" onclick="resetToDefaults()">恢复默认</button>
                                </div>
                            </div>
                            <div class="card-body p-0">
                                <div class="table-responsive">
                                    <table class="table table-hover mb-0" id="symbolsConfigTable">
                                        <thead class="table-light">
                                            <tr>
                                                <th class="d-none d-md-table-cell" width="80">启用</th>
                                                <th>交易对</th>
                                                <th class="d-none d-lg-table-cell">分类</th>
                                                <th width="80">手数</th>
                                                <th width="100">止损</th>
                                                <th width="100">止盈</th>
                                                <th class="d-none d-md-table-cell" width="80">超时</th>
                                                <th width="60">操作</th>
                                            </tr>
                                        </thead>
                                        <tbody id="symbolsConfigBody">
                                            <tr><td colspan="8" class="text-center text-muted">加载中...</td></tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-4 col-md-12">
                        <div class="card mb-3">
                            <div class="card-header">⚡ 快速配置</div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">按分类批量设置:</label>
                                    <select class="form-select form-select-sm" id="categorySelect">
                                        <option value="">选择分类</option>
                                        <option value="crypto">加密货币</option>
                                        <option value="forex">外汇</option>
                                        <option value="precious_metals">贵金属</option>
                                    </select>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <div class="mb-3">
                                            <label class="form-label">手数:</label>
                                            <input type="number" class="form-control form-control-sm" id="batchLotSize" step="0.01">
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="mb-3">
                                            <label class="form-label">超时(秒):</label>
                                            <input type="number" class="form-control form-control-sm" id="batchTimeout" value="180">
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-6">
                                        <div class="mb-3">
                                            <label class="form-label">止损(USD):</label>
                                            <input type="number" class="form-control form-control-sm" id="batchStopLoss" step="1">
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="mb-3">
                                            <label class="form-label">止盈(USD):</label>
                                            <input type="number" class="form-control form-control-sm" id="batchTakeProfit" step="1">
                                        </div>
                                    </div>
                                </div>
                                <button class="btn btn-primary w-100 btn-sm" onclick="applyBatchConfig()">
                                    应用批量配置
                                </button>
                            </div>
                        </div>
                        <div class="card">
                            <div class="card-header">📋 配置模板</div>
                            <div class="card-body">
                                <div class="d-grid gap-2">
                                    <button class="btn btn-outline-secondary btn-sm" onclick="applyTemplate('conservative')">
                                        🛡️ 保守模式
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="applyTemplate('balanced')">
                                        ⚖️ 平衡模式
                                    </button>
                                    <button class="btn btn-outline-secondary btn-sm" onclick="applyTemplate('aggressive')">
                                        🚀 激进模式
                                    </button>
                                </div>
                                <small class="text-muted mt-2 d-block">
                                    模板会根据交易对特性自动设置合适的参数
                                </small>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function getGlobalConfigHTML() {
            return `
                <div class="row">
                    <div class="col-lg-6 col-md-12">
                        <div class="card">
                            <div class="card-header">🌐 全局交易配置</div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">交易冷却时间 (秒):</label>
                                    <input type="number" class="form-control" id="globalTradingCooldown" min="60" value="300">
                                    <div class="form-text">两次交易之间的最小间隔时间</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">最大并发交易数:</label>
                                    <input type="number" class="form-control" id="globalMaxConcurrentTrades" min="1" max="20" value="5">
                                    <div class="form-text">同时持有的最大订单数量</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">全局止损金额 (USD):</label>
                                    <input type="number" class="form-control" id="globalStopLossUSD" min="50" step="50" value="500">
                                    <div class="form-text">组合总亏损达到此金额时全部平仓</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">全局止盈金额 (USD):</label>
                                    <input type="number" class="form-control" id="globalTakeProfitUSD" min="100" step="100" value="1000">
                                    <div class="form-text">组合总盈利达到此金额时全部平仓</div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="globalAutoTradingEnabled" checked>
                                        <label class="form-check-label" for="globalAutoTradingEnabled">
                                            启用自动交易
                                        </label>
                                    </div>
                                    <div class="form-text">关闭后将只记录警报，不执行交易</div>
                                </div>
                                <button class="btn btn-primary" onclick="saveGlobalConfig()">保存全局配置</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-12">
                        <div class="card">
                            <div class="card-header">📊 日交易限制</div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">日最大亏损 (USD):</label>
                                    <input type="number" class="form-control" id="globalMaxDailyLoss" min="50" step="50" value="500">
                                    <div class="form-text">当日亏损达到此金额时停止交易</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">日最大盈利 (USD):</label>
                                    <input type="number" class="form-control" id="globalMaxDailyProfit" min="100" step="100" value="1000">
                                    <div class="form-text">当日盈利达到此金额时停止交易</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">日最大交易次数:</label>
                                    <input type="number" class="form-control" id="globalMaxDailyTrades" min="1" value="50">
                                    <div class="form-text">每日最多执行的交易次数</div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="globalEmergencyStopEnabled" checked>
                                        <label class="form-check-label" for="globalEmergencyStopEnabled">
                                            启用紧急停止功能
                                        </label>
                                    </div>
                                </div>
                                <button class="btn btn-primary" onclick="saveDailyLimits()">保存日限制配置</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function getBarkConfigHTML() {
            return `
                <div class="row">
                    <div class="col-lg-6 col-md-12">
                        <div class="card">
                            <div class="card-header">🔔 Bark通知配置</div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="barkEnabled" checked>
                                        <label class="form-check-label" for="barkEnabled">
                                            启用Bark通知
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">设备Key 1:</label>
                                    <input type="text" class="form-control" id="barkDeviceKey1" placeholder="输入第一个设备的Key">
                                    <div class="form-text">主要通知设备</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">设备Key 2:</label>
                                    <input type="text" class="form-control" id="barkDeviceKey2" placeholder="输入第二个设备的Key">
                                    <div class="form-text">备用通知设备（可选）</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Bark服务器地址:</label>
                                    <input type="url" class="form-control" id="barkServerUrl" value="https://api.day.app" placeholder="https://api.day.app">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">通知音效:</label>
                                    <select class="form-select" id="barkSound">
                                        <option value="birdsong">birdsong</option>
                                        <option value="calypso">calypso</option>
                                        <option value="choo">choo</option>
                                        <option value="descent">descent</option>
                                        <option value="electronic">electronic</option>
                                        <option value="fanfare">fanfare</option>
                                        <option value="glass">glass</option>
                                        <option value="gotosleep">gotosleep</option>
                                        <option value="healthnotification">healthnotification</option>
                                        <option value="horn">horn</option>
                                        <option value="ladder">ladder</option>
                                        <option value="mailsent">mailsent</option>
                                        <option value="minuet">minuet</option>
                                        <option value="multiwayinvitation">multiwayinvitation</option>
                                        <option value="newmail">newmail</option>
                                        <option value="newsflash">newsflash</option>
                                        <option value="noir">noir</option>
                                        <option value="paymentsuccess">paymentsuccess</option>
                                        <option value="shake">shake</option>
                                        <option value="sherwoodforest">sherwoodforest</option>
                                        <option value="silence">silence</option>
                                        <option value="spell">spell</option>
                                        <option value="suspense">suspense</option>
                                        <option value="telegraph">telegraph</option>
                                        <option value="tiptoes">tiptoes</option>
                                        <option value="typewriters">typewriters</option>
                                        <option value="update">update</option>
                                    </select>
                                </div>
                                <button class="btn btn-primary me-2" onclick="saveBarkConfig()">保存配置</button>
                                <button class="btn btn-outline-secondary" onclick="testBarkNotification()">测试通知</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-12">
                        <div class="card">
                            <div class="card-header">📱 通知类型设置</div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="notifyTradeOpen" checked>
                                        <label class="form-check-label" for="notifyTradeOpen">
                                            交易开仓通知
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="notifyTradeClose" checked>
                                        <label class="form-check-label" for="notifyTradeClose">
                                            交易平仓通知
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="notifyAlertForward">
                                        <label class="form-check-label" for="notifyAlertForward">
                                            警报转发通知
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="notifySystemStatus" checked>
                                        <label class="form-check-label" for="notifySystemStatus">
                                            系统状态通知
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="notifyBalanceReport">
                                        <label class="form-check-label" for="notifyBalanceReport">
                                            定时余额报告
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">余额报告频率:</label>
                                    <select class="form-select" id="balanceReportInterval">
                                        <option value="1">每小时</option>
                                        <option value="4">每4小时</option>
                                        <option value="8">每8小时</option>
                                        <option value="12" selected>每12小时</option>
                                        <option value="24">每24小时</option>
                                    </select>
                                </div>
                                <button class="btn btn-primary" onclick="saveNotificationSettings()">保存通知设置</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function getRiskConfigHTML() {
            return `
                <div class="row">
                    <div class="col-lg-6 col-md-12">
                        <div class="card">
                            <div class="card-header">🛡️ 风险控制参数</div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">风险检查间隔 (秒):</label>
                                    <input type="number" class="form-control" id="riskCheckInterval" min="10" value="30">
                                    <div class="form-text">系统检查风险状态的频率</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">余额记录间隔 (分钟):</label>
                                    <input type="number" class="form-control" id="balanceRecordInterval" min="1" value="5">
                                    <div class="form-text">记录账户余额变化的频率</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">最大回撤百分比 (%):</label>
                                    <input type="number" class="form-control" id="maxDrawdownPercent" min="1" max="50" value="10">
                                    <div class="form-text">账户最大允许回撤比例</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">单笔交易最大风险 (%):</label>
                                    <input type="number" class="form-control" id="maxRiskPerTrade" min="0.1" max="10" step="0.1" value="2">
                                    <div class="form-text">单笔交易占账户资金的最大比例</div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="enableRiskControl" checked>
                                        <label class="form-check-label" for="enableRiskControl">
                                            启用风险控制
                                        </label>
                                    </div>
                                </div>
                                <button class="btn btn-primary" onclick="saveRiskConfig()">保存风险配置</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-12">
                        <div class="card">
                            <div class="card-header">⚠️ 紧急控制</div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">连续亏损次数限制:</label>
                                    <input type="number" class="form-control" id="maxConsecutiveLosses" min="1" value="5">
                                    <div class="form-text">连续亏损达到此次数时暂停交易</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">暂停交易时长 (分钟):</label>
                                    <input type="number" class="form-control" id="tradingPauseDuration" min="5" value="30">
                                    <div class="form-text">触发风险控制后的暂停时长</div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="enableEmergencyStop" checked>
                                        <label class="form-check-label" for="enableEmergencyStop">
                                            启用紧急停止
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="enableAutoRecovery">
                                        <label class="form-check-label" for="enableAutoRecovery">
                                            启用自动恢复
                                        </label>
                                    </div>
                                    <div class="form-text">暂停期结束后自动恢复交易</div>
                                </div>
                                <hr>
                                <div class="d-grid gap-2">
                                    <button class="btn btn-danger" onclick="triggerEmergencyStop()">
                                        🚨 立即紧急停止
                                    </button>
                                    <button class="btn btn-warning" onclick="pauseTrading()">
                                        ⏸️ 暂停交易
                                    </button>
                                    <button class="btn btn-success" onclick="resumeTrading()">
                                        ▶️ 恢复交易
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        function getSystemConfigHTML() {
            return `
                <div class="row">
                    <!-- MT5连接配置 -->
                    <div class="col-12 mb-4">
                        <div class="card">
                            <div class="card-header">
                                <h6 class="mb-0"><i class="bi bi-link-45deg text-primary"></i> MT5连接配置</h6>
                            </div>
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">MT5账号:</label>
                                            <input type="number" class="form-control" id="mt5Login" placeholder="输入MT5账号">
                                            <div class="form-text">您的MT5交易账号</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">MT5密码:</label>
                                            <input type="password" class="form-control" id="mt5Password" placeholder="输入MT5密码">
                                            <div class="form-text">您的MT5交易密码</div>
                                        </div>
                                    </div>
                                    <div class="col-md-4">
                                        <div class="mb-3">
                                            <label class="form-label">MT5服务器:</label>
                                            <input type="text" class="form-control" id="mt5Server" placeholder="例如: MetaQuotes-Demo">
                                            <div class="form-text">MT5服务器地址</div>
                                        </div>
                                    </div>
                                </div>
                                <div class="d-flex gap-2">
                                    <button class="btn btn-success btn-sm" onclick="testMT5ConnectionMain()">
                                        <i class="bi bi-wifi"></i> 测试连接
                                    </button>
                                    <button class="btn btn-info btn-sm" onclick="reconnectMT5Main()">
                                        <i class="bi bi-arrow-clockwise"></i> 重新连接
                                    </button>
                                    <button class="btn btn-primary btn-sm" onclick="saveMT5ConfigMain()">
                                        <i class="bi bi-save"></i> 保存MT5配置
                                    </button>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="col-lg-6 col-md-12">
                        <div class="card">
                            <div class="card-header">🔧 系统运行参数</div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">Webhook监听端口:</label>
                                    <input type="number" class="form-control" id="webhookPort" min="1000" max="65535" value="7000">
                                    <div class="form-text">接收TradingView警报的端口</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Web管理端口:</label>
                                    <input type="number" class="form-control" id="webPort" min="1000" max="65535" value="7000">
                                    <div class="form-text">Web管理界面访问端口</div>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">日志级别:</label>
                                    <select class="form-select" id="logLevel">
                                        <option value="DEBUG">DEBUG - 详细调试</option>
                                        <option value="INFO" selected>INFO - 一般信息</option>
                                        <option value="WARNING">WARNING - 警告信息</option>
                                        <option value="ERROR">ERROR - 错误信息</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">数据库备份间隔(小时):</label>
                                    <select class="form-select" id="backupInterval">
                                        <option value="6">6小时</option>
                                        <option value="12" selected>12小时</option>
                                        <option value="24">24小时</option>
                                        <option value="168">7天</option>
                                    </select>
                                </div>
                                <button class="btn btn-primary" onclick="saveSystemConfig()">保存系统配置</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6 col-md-12">
                        <div class="card">
                            <div class="card-header">📱 界面设置</div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">界面主题:</label>
                                    <select class="form-select" id="uiTheme">
                                        <option value="light" selected>浅色主题</option>
                                        <option value="dark">深色主题</option>
                                        <option value="auto">跟随系统</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">数据刷新间隔(秒):</label>
                                    <select class="form-select" id="refreshInterval">
                                        <option value="10">10秒</option>
                                        <option value="30" selected>30秒</option>
                                        <option value="60">60秒</option>
                                        <option value="120">2分钟</option>
                                    </select>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="soundEnabled" checked>
                                        <label class="form-check-label" for="soundEnabled">
                                            启用声音提示
                                        </label>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="form-check">
                                        <input class="form-check-input" type="checkbox" id="mobileOptimized" checked>
                                        <label class="form-check-label" for="mobileOptimized">
                                            移动端优化
                                        </label>
                                    </div>
                                </div>
                                <button class="btn btn-primary" onclick="saveUIConfig()">保存界面设置</button>
                            </div>
                        </div>
                        <div class="card mt-3">
                            <div class="card-header">🔗 系统信息</div>
                            <div class="card-body">
                                <div class="row mb-2">
                                    <div class="col-6"><strong>Webhook地址:</strong></div>
                                    <div class="col-6"><code>http://localhost:7000/webhook/tradingview</code></div>
                                </div>
                                <div class="row mb-2">
                                    <div class="col-6"><strong>Web管理地址:</strong></div>
                                    <div class="col-6"><code>http://localhost:7000</code></div>
                                </div>
                                <div class="row">
                                    <div class="col-6"><strong>WebSocket地址:</strong></div>
                                    <div class="col-6"><code>ws://localhost:7000/ws</code></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
        }

        // 配置更新函数
        async function updateSymbolConfig(symbol, key, value) {
            try {
                const response = await ui.apiCall(`/api/config/symbols/${symbol}`, 'PUT', {
                    [key]: value
                });
                if (response && response.success) {
                    ui.showAlert('success', `${symbol} 配置已更新`, 2000);
                } else {
                    ui.showAlert('danger', '配置更新失败');
                }
            } catch (error) {
                console.error('更新配置失败:', error);
                ui.showAlert('danger', '配置更新失败: ' + error.message);
            }
        }

        // 配置保存函数
        async function saveGlobalConfig() {
            try {
                const config = {
                    trading_cooldown: parseInt(document.getElementById('globalTradingCooldown').value),
                    max_concurrent_trades: parseInt(document.getElementById('globalMaxConcurrentTrades').value),
                    global_stop_loss_usd: parseFloat(document.getElementById('globalStopLossUSD').value),
                    global_take_profit_usd: parseFloat(document.getElementById('globalTakeProfitUSD').value),
                    auto_trading_enabled: document.getElementById('globalAutoTradingEnabled').checked
                };

                const response = await ui.apiCall('/api/config/global-sl-tp', 'PUT', config);
                if (response && response.success) {
                    ui.showAlert('success', '全局配置保存成功', 2000);
                } else {
                    ui.showAlert('danger', '保存全局配置失败');
                }
            } catch (error) {
                ui.showAlert('danger', '保存全局配置失败: ' + error.message);
            }
        }

        async function saveDailyLimits() {
            try {
                const config = {
                    max_daily_loss_usd: parseFloat(document.getElementById('globalMaxDailyLoss').value),
                    max_daily_profit_usd: parseFloat(document.getElementById('globalMaxDailyProfit').value),
                    max_daily_trades: parseInt(document.getElementById('globalMaxDailyTrades').value),
                    emergency_stop_enabled: document.getElementById('globalEmergencyStopEnabled').checked
                };

                const response = await ui.apiCall('/api/config/daily-limits', 'PUT', config);
                if (response && response.success) {
                    ui.showAlert('success', '日限制配置保存成功', 2000);
                } else {
                    ui.showAlert('danger', '保存日限制配置失败');
                }
            } catch (error) {
                ui.showAlert('danger', '保存日限制配置失败: ' + error.message);
            }
        }

        async function saveBarkConfig() {
            try {
                const config = {
                    enabled: document.getElementById('barkEnabled').checked,
                    device_keys: [
                        document.getElementById('barkDeviceKey1').value.trim(),
                        document.getElementById('barkDeviceKey2').value.trim()
                    ].filter(key => key.length > 0),
                    server_url: document.getElementById('barkServerUrl').value.trim() || 'https://api.day.app',
                    sound: document.getElementById('barkSound').value || 'birdsong',
                    title: '交易通知',
                    level: 'active',
                    volume: 5,
                    group: 'trading',
                    trade_open: document.getElementById('notifyTradeOpen')?.checked !== false,
                    trade_close: document.getElementById('notifyTradeClose')?.checked !== false,
                    alert_forward: document.getElementById('notifyAlertForward')?.checked === true,
                    system_status: document.getElementById('notifySystemStatus')?.checked !== false,
                    balance_report: document.getElementById('notifyBalanceReport')?.checked === true,
                    balance_report_interval: parseInt(document.getElementById('balanceReportInterval')?.value || '12')
                };

                console.log('保存Bark配置:', config);

                const response = await ui.apiCall('/api/config/bark', 'PUT', config);
                if (response && response.success) {
                    ui.showAlert('success', 'Bark配置保存成功', 2000);
                    console.log('Bark配置保存成功');
                } else {
                    const errorMsg = response ? (response.error || response.message || '未知错误') : '请求失败';
                    ui.showAlert('danger', '保存Bark配置失败: ' + errorMsg);
                    console.error('保存Bark配置失败:', response);
                }
            } catch (error) {
                console.error('保存Bark配置异常:', error);
                ui.showAlert('danger', '保存Bark配置失败: ' + error.message);
            }
        }

        async function testBarkNotification() {
            try {
                const deviceKey1 = document.getElementById('barkDeviceKey1').value.trim();
                const deviceKey2 = document.getElementById('barkDeviceKey2').value.trim();

                if (!deviceKey1 && !deviceKey2) {
                    ui.showAlert('warning', '请先配置至少一个设备Key');
                    return;
                }

                const testData = {
                    type: 'bark',
                    message: '这是一条来自交易系统的测试通知 🎉'
                };

                console.log('发送测试通知:', testData);

                const response = await ui.apiCall('/api/notifications/test', 'POST', testData);
                if (response && response.success) {
                    ui.showAlert('success', response.message || '测试通知发送成功', 3000);
                    console.log('测试通知发送成功');
                } else {
                    const errorMsg = response ? (response.error || response.message || '发送失败') : '请求失败';
                    ui.showAlert('danger', '测试通知发送失败: ' + errorMsg);
                    console.error('测试通知发送失败:', response);
                }
            } catch (error) {
                console.error('测试通知异常:', error);
                ui.showAlert('danger', '测试通知失败: ' + error.message);
            }
        }

        async function saveNotificationSettings() {
            try {
                // 获取当前的Bark配置
                const barkResponse = await ui.apiCall('/api/config/bark', 'GET');
                let barkConfig = {};

                if (barkResponse && barkResponse.success) {
                    barkConfig = barkResponse.data;
                }

                // 更新通知设置
                const updatedConfig = {
                    ...barkConfig,
                    trade_open: document.getElementById('notifyTradeOpen').checked,
                    trade_close: document.getElementById('notifyTradeClose').checked,
                    alert_forward: document.getElementById('notifyAlertForward').checked,
                    system_status: document.getElementById('notifySystemStatus').checked,
                    balance_report: document.getElementById('notifyBalanceReport').checked,
                    balance_report_interval: parseInt(document.getElementById('balanceReportInterval').value)
                };

                // 保存到Bark配置
                const response = await ui.apiCall('/api/config/bark', 'PUT', updatedConfig);
                if (response && response.success) {
                    ui.showAlert('success', '通知设置保存成功', 2000);
                } else {
                    ui.showAlert('danger', '保存通知设置失败');
                }
            } catch (error) {
                console.error('保存通知设置异常:', error);
                ui.showAlert('danger', '保存通知设置失败: ' + error.message);
            }
        }

        async function saveRiskConfig() {
            try {
                const config = {
                    risk_check_interval: parseInt(document.getElementById('riskCheckInterval').value),
                    balance_record_interval: parseInt(document.getElementById('balanceRecordInterval').value),
                    max_drawdown_percent: parseFloat(document.getElementById('maxDrawdownPercent').value),
                    max_risk_per_trade: parseFloat(document.getElementById('maxRiskPerTrade').value),
                    max_consecutive_losses: parseInt(document.getElementById('maxConsecutiveLosses').value),
                    trading_pause_duration: parseInt(document.getElementById('tradingPauseDuration').value),
                    enable_risk_control: document.getElementById('enableRiskControl').checked,
                    enable_emergency_stop: document.getElementById('enableEmergencyStop').checked,
                    enable_auto_recovery: document.getElementById('enableAutoRecovery').checked
                };

                const response = await fetch('/api/config/risk', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(config)
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data && data.success) {
                        showAlert('success', '风险配置保存成功', 2000);
                    } else {
                        showAlert('danger', '保存风险配置失败: ' + (data?.error || '未知错误'));
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                console.error('保存风险配置失败:', error);
                showAlert('danger', '保存风险配置失败: ' + error.message);
            }
        }

        async function saveUIConfig() {
            try {
                const config = {
                    ui_theme: document.getElementById('uiTheme').value,
                    refresh_interval: parseInt(document.getElementById('refreshInterval').value),
                    sound_enabled: document.getElementById('soundEnabled').checked,
                    mobile_optimized: document.getElementById('mobileOptimized').checked
                };

                const response = await ui.apiCall('/api/config/ui', 'PUT', config);
                if (response && response.success) {
                    ui.showAlert('success', '界面设置保存成功', 2000);
                } else {
                    ui.showAlert('danger', '保存界面设置失败');
                }
            } catch (error) {
                ui.showAlert('danger', '保存界面设置失败: ' + error.message);
            }
        }

        async function saveSystemConfig() {
            try {
                const config = {
                    webhook_port: parseInt(document.getElementById('webhookPort').value),
                    web_port: parseInt(document.getElementById('webPort').value),
                    log_level: document.getElementById('logLevel').value,
                    backup_interval: parseInt(document.getElementById('backupInterval').value)
                };

                const response = await ui.apiCall('/api/config/system', 'PUT', config);
                if (response && response.success) {
                    ui.showAlert('success', '系统配置保存成功', 2000);
                } else {
                    ui.showAlert('danger', '保存系统配置失败: ' + (response.error || '未知错误'));
                }
            } catch (error) {
                ui.showAlert('danger', '保存系统配置失败: ' + error.message);
            }
        }

        // 配置模板和批量操作函数
        async function applyTemplate(templateType) {
            const templates = {
                conservative: {
                    lot_size: 0.01,
                    stop_loss_usd: 30,
                    take_profit_usd: 60,
                    signal_timeout_seconds: 300
                },
                balanced: {
                    lot_size: 0.02,
                    stop_loss_usd: 50,
                    take_profit_usd: 100,
                    signal_timeout_seconds: 180
                },
                aggressive: {
                    lot_size: 0.05,
                    stop_loss_usd: 100,
                    take_profit_usd: 200,
                    signal_timeout_seconds: 120
                }
            };

            const template = templates[templateType];
            if (!template) return;

            if (confirm(`确定要应用${templateType === 'conservative' ? '保守' : templateType === 'balanced' ? '平衡' : '激进'}模式配置吗？这将覆盖所有交易对的当前设置。`)) {
                try {
                    const response = await ui.apiCall('/api/config/symbols/batch', 'POST', {
                        template: templateType,
                        config: template
                    });

                    if (response && response.success) {
                        ui.showAlert('success', `${templateType === 'conservative' ? '保守' : templateType === 'balanced' ? '平衡' : '激进'}模式配置应用成功`);
                        await loadSymbolConfigs();
                    } else {
                        ui.showAlert('danger', '应用配置模板失败');
                    }
                } catch (error) {
                    ui.showAlert('danger', '应用配置模板失败: ' + error.message);
                }
            }
        }

        async function applyBatchConfig() {
            console.log('开始应用批量配置...');

            const category = document.getElementById('categorySelect').value;
            console.log('选择的分类:', category);

            if (!category) {
                ui.showAlert('warning', '请先选择交易对分类');
                return;
            }

            const lotSize = document.getElementById('batchLotSize').value;
            const stopLoss = document.getElementById('batchStopLoss').value;
            const takeProfit = document.getElementById('batchTakeProfit').value;
            const timeout = document.getElementById('batchTimeout').value;

            console.log('表单值:', { lotSize, stopLoss, takeProfit, timeout });

            // 构建配置对象，只包含有值的字段
            const config = {};
            if (lotSize && !isNaN(parseFloat(lotSize)) && parseFloat(lotSize) > 0) {
                config.lot_size = parseFloat(lotSize);
            }
            if (stopLoss && !isNaN(parseInt(stopLoss)) && parseInt(stopLoss) > 0) {
                config.stop_loss_usd = parseInt(stopLoss);
            }
            if (takeProfit && !isNaN(parseInt(takeProfit)) && parseInt(takeProfit) > 0) {
                config.take_profit_usd = parseInt(takeProfit);
            }
            if (timeout && !isNaN(parseInt(timeout)) && parseInt(timeout) > 0) {
                config.signal_timeout_seconds = parseInt(timeout);
            }

            console.log('构建的配置:', config);

            // 验证至少有一个配置项
            if (Object.keys(config).length === 0) {
                ui.showAlert('warning', '请至少填写一个有效的配置项（大于0的数值）');
                return;
            }

            // 获取分类名称
            const categoryNames = {
                'crypto': '加密货币',
                'forex': '外汇',
                'precious_metals': '贵金属'
            };

            try {
                console.log('发送批量配置请求...');
                const response = await ui.apiCall('/api/config/symbols/batch', 'POST', {
                    category: category,
                    config: config
                });

                console.log('批量配置响应:', response);

                if (response && response.success) {
                    ui.showAlert('success', `${categoryNames[category] || category} 分类批量配置应用成功`);
                    await loadSymbolConfigs();

                    // 清空表单（除了超时保持默认值）
                    document.getElementById('batchLotSize').value = '';
                    document.getElementById('batchStopLoss').value = '';
                    document.getElementById('batchTakeProfit').value = '';
                    document.getElementById('categorySelect').value = '';
                } else {
                    const errorMsg = response ? (response.error || response.message || '未知错误') : '请求失败';
                    ui.showAlert('danger', '批量配置失败: ' + errorMsg);
                }
            } catch (error) {
                console.error('批量配置异常:', error);
                ui.showAlert('danger', '批量配置失败: ' + error.message);
            }
        }

        async function resetToDefaults() {
            if (confirm('确定要恢复所有交易对的默认配置吗？这将覆盖当前所有设置。')) {
                try {
                    const response = await ui.apiCall('/api/config/symbols/reset', 'POST');
                    if (response && response.success) {
                        ui.showAlert('success', '已恢复默认配置');
                        await loadSymbolConfigs();
                    } else {
                        ui.showAlert('danger', '恢复默认配置失败');
                    }
                } catch (error) {
                    ui.showAlert('danger', '恢复默认配置失败: ' + error.message);
                }
            }
        }

        // 测试和控制函数
        async function testBarkNotification() {
            try {
                const response = await ui.apiCall('/api/notifications/test', 'POST', {
                    type: 'bark',
                    message: '这是一条测试通知'
                });

                if (response && response.success) {
                    ui.showAlert('success', '测试通知已发送');
                } else {
                    ui.showAlert('danger', '发送测试通知失败');
                }
            } catch (error) {
                ui.showAlert('danger', '发送测试通知失败: ' + error.message);
            }
        }

        async function triggerEmergencyStop() {
            if (confirm('确定要执行紧急停止吗？这将立即关闭所有持仓并停止策略。')) {
                try {
                    const response = await ui.apiCall('/api/strategy/emergency-stop', 'POST');
                    if (response && response.success) {
                        ui.showAlert('warning', '紧急停止已执行');
                        ui.loadData();
                    } else {
                        ui.showAlert('danger', '紧急停止失败');
                    }
                } catch (error) {
                    ui.showAlert('danger', '紧急停止失败: ' + error.message);
                }
            }
        }

        async function pauseTrading() {
            try {
                const response = await ui.apiCall('/api/strategy/pause', 'POST');
                if (response && response.success) {
                    ui.showAlert('info', '交易已暂停');
                } else {
                    ui.showAlert('danger', '暂停交易失败');
                }
            } catch (error) {
                ui.showAlert('danger', '暂停交易失败: ' + error.message);
            }
        }

        async function resumeTrading() {
            try {
                const response = await ui.apiCall('/api/strategy/resume', 'POST');
                if (response && response.success) {
                    ui.showAlert('success', '交易已恢复');
                } else {
                    ui.showAlert('danger', '恢复交易失败');
                }
            } catch (error) {
                ui.showAlert('danger', '恢复交易失败: ' + error.message);
            }
        }

        // 系统配置模态框
        async function showSystemConfigModal() {
            try {
                const response = await ui.apiCall('/api/config/system');
                if (response && response.success) {
                    const config = response.data;
                    showConfigModal(config);
                }
            } catch (error) {
                ui.showAlert('danger', '加载系统配置失败');
            }
        }

        function showConfigModal(config) {
            const modalHtml = `
                <div class="modal fade" id="systemConfigModal" tabindex="-1">
                    <div class="modal-dialog modal-lg">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title"><i class="bi bi-gear"></i> 系统配置</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="systemConfigForm">
                                    <!-- MT5连接配置 -->
                                    <h6 class="mb-3 text-primary"><i class="bi bi-link-45deg"></i> MT5连接配置</h6>
                                    <div class="row">
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">MT5账号</label>
                                                <input type="number" class="form-control" name="mt5_login"
                                                       value="${config.mt5_login || ''}" placeholder="输入MT5账号">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">MT5密码</label>
                                                <input type="password" class="form-control" name="mt5_password"
                                                       value="${config.mt5_password || ''}" placeholder="输入MT5密码">
                                            </div>
                                        </div>
                                        <div class="col-md-4">
                                            <div class="mb-3">
                                                <label class="form-label">MT5服务器</label>
                                                <input type="text" class="form-control" name="mt5_server"
                                                       value="${config.mt5_server || ''}" placeholder="例如: MetaQuotes-Demo">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="d-flex gap-2 mb-3">
                                                <button type="button" class="btn btn-success btn-sm" onclick="testMT5Connection()">
                                                    <i class="bi bi-wifi"></i> 测试连接
                                                </button>
                                                <button type="button" class="btn btn-info btn-sm" onclick="reconnectMT5()">
                                                    <i class="bi bi-arrow-clockwise"></i> 重新连接
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    <hr class="my-4">

                                    <!-- 交易配置 -->
                                    <h6 class="mb-3 text-secondary"><i class="bi bi-gear"></i> 交易配置</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">交易冷却时间 (秒)</label>
                                                <input type="number" class="form-control" name="trading_cooldown"
                                                       value="${config.trading_cooldown || 300}" min="60">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">最大并发交易数</label>
                                                <input type="number" class="form-control" name="max_concurrent_trades"
                                                       value="${config.max_concurrent_trades || 5}" min="1">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">日最大亏损 (USD)</label>
                                                <input type="number" class="form-control" name="max_daily_loss_usd"
                                                       value="${config.max_daily_loss_usd || 500}" min="50">
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="mb-3">
                                                <label class="form-label">日最大盈利 (USD)</label>
                                                <input type="number" class="form-control" name="max_daily_profit_usd"
                                                       value="${config.max_daily_profit_usd || 1000}" min="100">
                                            </div>
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="auto_trading_enabled"
                                                       ${config.auto_trading_enabled !== false ? 'checked' : ''}>
                                                <label class="form-check-label">启用自动交易</label>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-check">
                                                <input class="form-check-input" type="checkbox" name="bark_forward_enabled"
                                                       ${config.bark_forward_enabled === 'True' ? 'checked' : ''}>
                                                <label class="form-check-label">启用Bark通知</label>
                                            </div>
                                        </div>
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-primary" onclick="saveSystemConfigModal()">保存配置</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除旧模态框
            const oldModal = document.getElementById('systemConfigModal');
            if (oldModal) oldModal.remove();

            // 添加新模态框
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('systemConfigModal'));
            modal.show();
        }

        async function saveSystemConfigModal() {
            try {
                const form = document.getElementById('systemConfigForm');
                if (!form) {
                    ui.showAlert('danger', '找不到配置表单');
                    return;
                }

                const formData = new FormData(form);
                const config = {};

                // 收集表单数据
                for (let [key, value] of formData.entries()) {
                    const field = form.querySelector(`[name="${key}"]`);
                    if (field) {
                        if (field.type === 'checkbox') {
                            config[key] = field.checked;
                        } else if (field.type === 'number') {
                            config[key] = parseFloat(value) || 0;
                        } else {
                            config[key] = value;
                        }
                    }
                }

                // 分离MT5配置
                const mt5Config = {};
                const systemConfig = {};

                Object.keys(config).forEach(key => {
                    if (key.startsWith('mt5_')) {
                        mt5Config[key] = config[key];
                    } else {
                        systemConfig[key] = config[key];
                    }
                });

                console.log('模态框保存配置:', { systemConfig, mt5Config });

                let allSuccess = true;
                let messages = [];

                // 保存系统配置
                if (Object.keys(systemConfig).length > 0) {
                    const systemResponse = await ui.apiCall('/api/config/system', 'PUT', systemConfig);
                    console.log('系统配置保存响应:', systemResponse);

                    if (systemResponse && systemResponse.success) {
                        messages.push('系统配置保存成功');
                    } else {
                        allSuccess = false;
                        const errorMsg = systemResponse ? (systemResponse.error || systemResponse.message || '未知错误') : '请求失败';
                        messages.push('系统配置保存失败: ' + errorMsg);
                    }
                }

                // 保存MT5配置
                if (Object.keys(mt5Config).length > 0) {
                    const mt5Response = await ui.apiCall('/api/config/mt5', 'PUT', mt5Config);
                    console.log('MT5配置保存响应:', mt5Response);

                    if (mt5Response && mt5Response.success) {
                        messages.push('MT5配置保存成功');
                    } else {
                        allSuccess = false;
                        const errorMsg = mt5Response ? (mt5Response.error || mt5Response.message || '未知错误') : '请求失败';
                        messages.push('MT5配置保存失败: ' + errorMsg);
                    }
                }

                if (allSuccess) {
                    ui.showAlert('success', messages.join('，'));
                    const modal = bootstrap.Modal.getInstance(document.getElementById('systemConfigModal'));
                    if (modal) {
                        modal.hide();
                    }
                    // 重新加载配置数据
                    await loadAllConfigData();
                } else {
                    ui.showAlert('danger', messages.join('，'));
                }
            } catch (error) {
                console.error('模态框保存配置失败:', error);
                ui.showAlert('danger', '保存配置失败: ' + error.message);
            }
        }

        // 主配置页面的MT5函数
        async function testMT5ConnectionMain() {
            try {
                const login = document.getElementById('mt5Login').value;
                const password = document.getElementById('mt5Password').value;
                const server = document.getElementById('mt5Server').value;

                if (!login || !password || !server) {
                    ui.showAlert('warning', '请填写完整的MT5连接信息');
                    return;
                }

                ui.showAlert('info', '正在测试MT5连接...', 2000);

                const response = await ui.apiCall('/api/mt5/test-connection', 'POST', {
                    login: parseInt(login),
                    password: password,
                    server: server
                });

                if (response && response.success) {
                    ui.showAlert('success', 'MT5连接测试成功！', 3000);
                } else {
                    const errorMsg = response ? (response.error || response.message || '连接失败') : '请求失败';
                    ui.showAlert('danger', 'MT5连接测试失败: ' + errorMsg);
                }
            } catch (error) {
                console.error('MT5连接测试异常:', error);
                ui.showAlert('danger', 'MT5连接测试失败: ' + error.message);
            }
        }

        async function reconnectMT5Main() {
            try {
                ui.showAlert('info', '正在重新连接MT5...', 2000);

                const response = await ui.apiCall('/api/mt5/reconnect', 'POST');

                if (response && response.success) {
                    ui.showAlert('success', 'MT5重新连接成功！', 3000);
                    // 刷新系统状态
                    await ui.loadData();
                } else {
                    const errorMsg = response ? (response.error || response.message || '重连失败') : '请求失败';
                    ui.showAlert('danger', 'MT5重新连接失败: ' + errorMsg);
                }
            } catch (error) {
                console.error('MT5重连异常:', error);
                ui.showAlert('danger', 'MT5重新连接失败: ' + error.message);
            }
        }

        async function saveMT5ConfigMain() {
            try {
                const login = document.getElementById('mt5Login').value;
                const password = document.getElementById('mt5Password').value;
                const server = document.getElementById('mt5Server').value;

                if (!login || !password || !server) {
                    ui.showAlert('warning', '请填写完整的MT5配置信息');
                    return;
                }

                const mt5Config = {
                    mt5_login: parseInt(login),
                    mt5_password: password,
                    mt5_server: server
                };

                const response = await ui.apiCall('/api/config/mt5', 'PUT', mt5Config);

                if (response && response.success) {
                    ui.showAlert('success', 'MT5配置保存成功！', 3000);
                } else {
                    const errorMsg = response ? (response.error || response.message || '保存失败') : '请求失败';
                    ui.showAlert('danger', 'MT5配置保存失败: ' + errorMsg);
                }
            } catch (error) {
                console.error('MT5配置保存异常:', error);
                ui.showAlert('danger', 'MT5配置保存失败: ' + error.message);
            }
        }

        // 模态框中的MT5连接相关函数
        async function testMT5Connection() {
            try {
                const login = document.querySelector('[name="mt5_login"]').value;
                const password = document.querySelector('[name="mt5_password"]').value;
                const server = document.querySelector('[name="mt5_server"]').value;

                if (!login || !password || !server) {
                    ui.showAlert('warning', '请填写完整的MT5连接信息');
                    return;
                }

                ui.showAlert('info', '正在测试MT5连接...', 2000);

                const response = await ui.apiCall('/api/mt5/test-connection', 'POST', {
                    login: parseInt(login),
                    password: password,
                    server: server
                });

                if (response && response.success) {
                    ui.showAlert('success', 'MT5连接测试成功！', 3000);
                } else {
                    const errorMsg = response ? (response.error || response.message || '连接失败') : '请求失败';
                    ui.showAlert('danger', 'MT5连接测试失败: ' + errorMsg);
                }
            } catch (error) {
                console.error('MT5连接测试异常:', error);
                ui.showAlert('danger', 'MT5连接测试失败: ' + error.message);
            }
        }

        async function reconnectMT5() {
            try {
                ui.showAlert('info', '正在重新连接MT5...', 2000);

                const response = await ui.apiCall('/api/mt5/reconnect', 'POST');

                if (response && response.success) {
                    ui.showAlert('success', 'MT5重新连接成功！', 3000);
                    // 刷新系统状态
                    await ui.loadData();
                } else {
                    const errorMsg = response ? (response.error || response.message || '重连失败') : '请求失败';
                    ui.showAlert('danger', 'MT5重新连接失败: ' + errorMsg);
                }
            } catch (error) {
                console.error('MT5重连异常:', error);
                ui.showAlert('danger', 'MT5重新连接失败: ' + error.message);
            }
        }

        // 价格相关函数
        let priceUpdateInterval = null;
        let lastPrices = {};

        async function loadMarketPrices() {
            try {
                const response = await ui.apiCall('/api/market/prices');
                if (response && response.success) {
                    updatePriceDisplay(response.data);
                } else {
                    console.error('获取价格失败:', response);
                }
            } catch (error) {
                console.error('加载市场价格失败:', error);
            }
        }

        function updatePriceDisplay(pricesData) {
            const symbols = ['BTCUSD', 'ETHUSD', 'SOLUSD', 'XAUUSD', 'GBPJPY'];

            symbols.forEach(symbol => {
                const priceData = pricesData[symbol];
                if (priceData) {
                    const priceElement = document.getElementById(`price-${symbol}`);
                    const changeElement = document.getElementById(`change-${symbol}`);

                    if (priceElement && changeElement) {
                        // 格式化价格显示
                        let displayPrice = '';
                        if (priceData.last > 0) {
                            if (symbol === 'XAUUSD' || symbol === 'GBPJPY') {
                                displayPrice = priceData.last.toFixed(2);
                            } else {
                                displayPrice = priceData.last.toFixed(4);
                            }
                        } else if (priceData.bid > 0) {
                            if (symbol === 'XAUUSD' || symbol === 'GBPJPY') {
                                displayPrice = priceData.bid.toFixed(2);
                            } else {
                                displayPrice = priceData.bid.toFixed(4);
                            }
                        } else {
                            displayPrice = '--';
                        }

                        // 检查价格是否有变化
                        const currentPrice = priceData.last || priceData.bid;
                        const lastPrice = lastPrices[symbol];

                        if (lastPrice && currentPrice !== lastPrice) {
                            // 添加更新动画
                            priceElement.classList.add('price-update');
                            setTimeout(() => {
                                priceElement.classList.remove('price-update');
                            }, 500);
                        }

                        // 更新价格显示
                        priceElement.textContent = displayPrice;

                        // 更新涨跌显示
                        if (priceData.spread > 0) {
                            changeElement.textContent = `点差: ${priceData.spread.toFixed(priceData.spread < 1 ? 5 : 2)}`;
                            changeElement.className = 'price-change neutral';
                        } else if (priceData.error) {
                            changeElement.textContent = '无数据';
                            changeElement.className = 'price-change neutral';
                        } else {
                            changeElement.textContent = '实时';
                            changeElement.className = 'price-change neutral';
                        }

                        // 保存当前价格
                        lastPrices[symbol] = currentPrice;
                    }
                }
            });

            // 更新时间显示
            const timeElement = document.getElementById('priceUpdateTime');
            if (timeElement) {
                const now = new Date();
                timeElement.textContent = now.toLocaleTimeString();
            }
        }

        async function refreshPrices() {
            await loadMarketPrices();
        }

        function startPriceUpdates() {
            // 立即加载一次
            loadMarketPrices();

            // 设置定时更新（每5秒）
            if (priceUpdateInterval) {
                clearInterval(priceUpdateInterval);
            }
            priceUpdateInterval = setInterval(loadMarketPrices, 5000);
        }

        function stopPriceUpdates() {
            if (priceUpdateInterval) {
                clearInterval(priceUpdateInterval);
                priceUpdateInterval = null;
            }
        }

        // 系统状态管理函数
        async function loadSystemStatus() {
            try {
                const response = await ui.apiCall('/api/system/full-status');
                if (response && response.success) {
                    updateSystemStatusDisplay(response.data);
                }

                // 同时获取MT5连接统计信息
                const mt5StatsResponse = await ui.apiCall('/api/mt5/connection-stats');
                if (mt5StatsResponse && mt5StatsResponse.success) {
                    updateMT5StatsDisplay(mt5StatsResponse.data);
                }

                // 获取超时监控状态
                const timeoutResponse = await ui.apiCall('/api/timeout-monitor/status');
                if (timeoutResponse && timeoutResponse.success) {
                    updateTimeoutMonitorDisplay(timeoutResponse.data);
                }
            } catch (error) {
                console.error('加载系统状态失败:', error);
            }
        }

        function updateSystemStatusDisplay(statusData) {
            // 更新交易状态
            const tradingIndicator = document.getElementById('tradingStatusIndicator');
            const tradingText = document.getElementById('tradingStatusText');
            const enableBtn = document.getElementById('enableTradingBtn');
            const pauseBtn = document.getElementById('pauseTradingBtn');

            // 底部状态栏元素
            const footerTradingIndicator = document.getElementById('footerTradingStatusIndicator');
            const footerTradingText = document.getElementById('footerTradingStatusText');
            const footerEnableBtn = document.getElementById('footerEnableTradingBtn');
            const footerPauseBtn = document.getElementById('footerPauseTradingBtn');

            if (statusData.trading_enabled) {
                // 主页面状态
                if (tradingIndicator) tradingIndicator.className = 'status-indicator status-online';
                if (tradingText) tradingText.textContent = '交易中';
                if (enableBtn) enableBtn.disabled = true;
                if (pauseBtn) pauseBtn.disabled = false;

                // 底部状态栏
                if (footerTradingIndicator) footerTradingIndicator.className = 'status-indicator status-online';
                if (footerTradingText) footerTradingText.textContent = '交易中';
                if (footerEnableBtn) footerEnableBtn.disabled = true;
                if (footerPauseBtn) footerPauseBtn.disabled = false;
            } else {
                // 主页面状态
                if (tradingIndicator) tradingIndicator.className = 'status-indicator status-paused';
                if (tradingText) tradingText.textContent = '已暂停';
                if (enableBtn) enableBtn.disabled = false;
                if (pauseBtn) pauseBtn.disabled = true;

                // 底部状态栏
                if (footerTradingIndicator) footerTradingIndicator.className = 'status-indicator status-paused';
                if (footerTradingText) footerTradingText.textContent = '已暂停';
                if (footerEnableBtn) footerEnableBtn.disabled = false;
                if (footerPauseBtn) footerPauseBtn.disabled = true;
            }

            // 更新Webhook状态
            const webhookIndicator = document.getElementById('webhookStatusIndicator');
            const webhookText = document.getElementById('webhookStatusText');
            const footerWebhookIndicator = document.getElementById('footerWebhookStatusIndicator');
            const footerWebhookText = document.getElementById('footerWebhookStatusText');

            if (statusData.webhook_running) {
                if (webhookIndicator) webhookIndicator.className = 'status-indicator status-online';
                if (webhookText) webhookText.textContent = `运行中 :${statusData.webhook_port}`;
                if (footerWebhookIndicator) footerWebhookIndicator.className = 'status-indicator status-online';
                if (footerWebhookText) footerWebhookText.textContent = `运行中 :${statusData.webhook_port}`;
            } else {
                if (webhookIndicator) webhookIndicator.className = 'status-indicator status-offline';
                if (webhookText) webhookText.textContent = '未运行';
                if (footerWebhookIndicator) footerWebhookIndicator.className = 'status-indicator status-offline';
                if (footerWebhookText) footerWebhookText.textContent = '未运行';
            }

            // 更新MT5状态
            const mt5Indicator = document.getElementById('mt5StatusIndicator');
            const mt5Text = document.getElementById('mt5StatusText');
            const footerMt5Indicator = document.getElementById('footerMt5StatusIndicator');
            const footerMt5Text = document.getElementById('footerMt5StatusText');

            if (statusData.mt5_connected) {
                if (mt5Indicator) mt5Indicator.className = 'status-indicator status-online';
                if (mt5Text) mt5Text.textContent = '已连接';
                if (footerMt5Indicator) footerMt5Indicator.className = 'status-indicator status-online';
                if (footerMt5Text) footerMt5Text.textContent = '已连接';
            } else {
                if (mt5Indicator) mt5Indicator.className = 'status-indicator status-offline';
                if (mt5Text) mt5Text.textContent = '未连接';
                if (footerMt5Indicator) footerMt5Indicator.className = 'status-indicator status-offline';
                if (footerMt5Text) footerMt5Text.textContent = '未连接';
            }

            // 更新快速状态显示
            const quickStatus = document.getElementById('quickSystemStatus');
            if (quickStatus) {
                if (statusData.trading_enabled && statusData.webhook_running && statusData.mt5_connected) {
                    quickStatus.textContent = '运行中';
                    quickStatus.className = 'badge bg-success';
                } else if (statusData.trading_enabled || statusData.webhook_running || statusData.mt5_connected) {
                    quickStatus.textContent = '部分运行';
                    quickStatus.className = 'badge bg-warning text-dark';
                } else {
                    quickStatus.textContent = '已停止';
                    quickStatus.className = 'badge bg-danger';
                }
            }

            // 更新系统运行时间
            const uptimeText = document.getElementById('systemUptimeText');
            if (uptimeText) {
                uptimeText.textContent = statusData.uptime_text || '--';
            }
        }

        async function enableTrading() {
            try {
                const response = await ui.apiCall('/api/system/trading-status', 'POST', {
                    enabled: true
                });

                if (response && response.success) {
                    ui.showAlert('success', response.message || '交易已启用', 3000);
                    await loadSystemStatus();
                } else {
                    const errorMsg = response ? (response.error || response.message || '启用失败') : '请求失败';
                    ui.showAlert('danger', '启用交易失败: ' + errorMsg);
                }
            } catch (error) {
                console.error('启用交易失败:', error);
                ui.showAlert('danger', '启用交易失败: ' + error.message);
            }
        }

        async function pauseTrading() {
            if (!confirm('确定要暂停自动交易吗？\\n\\n暂停后系统将不会处理新的交易信号。')) {
                return;
            }

            try {
                const response = await ui.apiCall('/api/system/trading-status', 'POST', {
                    enabled: false
                });

                if (response && response.success) {
                    ui.showAlert('warning', response.message || '交易已暂停', 3000);
                    await loadSystemStatus();
                } else {
                    const errorMsg = response ? (response.error || response.message || '暂停失败') : '请求失败';
                    ui.showAlert('danger', '暂停交易失败: ' + errorMsg);
                }
            } catch (error) {
                console.error('暂停交易失败:', error);
                ui.showAlert('danger', '暂停交易失败: ' + error.message);
            }
        }

        function updateMT5StatsDisplay(statsData) {
            const mt5ReconnectInfo = document.getElementById('mt5ReconnectInfo');
            if (mt5ReconnectInfo && statsData) {
                let infoText = '';

                if (statsData.reconnect_attempts > 0) {
                    infoText = `重连: ${statsData.reconnect_attempts}/${statsData.max_reconnect_attempts}`;
                    if (!statsData.auto_reconnect_enabled) {
                        infoText += ' (已停用)';
                    }
                } else if (statsData.is_connected) {
                    infoText = '连接稳定';
                } else {
                    infoText = '等待连接';
                }

                if (statsData.monitor_thread_alive) {
                    infoText += ' | 监控中';
                }

                mt5ReconnectInfo.textContent = infoText;

                // 根据重连状态设置颜色
                if (statsData.reconnect_attempts >= statsData.max_reconnect_attempts) {
                    mt5ReconnectInfo.style.color = '#dc3545'; // 红色
                } else if (statsData.reconnect_attempts > 0) {
                    mt5ReconnectInfo.style.color = '#ffc107'; // 黄色
                } else {
                    mt5ReconnectInfo.style.color = '#6c757d'; // 灰色
                }
            }
        }

        async function resetMT5Reconnect() {
            if (!confirm('确定要重置MT5重连计数吗？\\n\\n这将重新启用自动重连功能。')) {
                return;
            }

            try {
                const response = await ui.apiCall('/api/mt5/reset-reconnect', 'POST');

                if (response && response.success) {
                    ui.showAlert('success', response.message || 'MT5重连已重置', 3000);
                    await loadSystemStatus();
                } else {
                    const errorMsg = response ? (response.error || response.message || '重置失败') : '请求失败';
                    ui.showAlert('danger', '重置MT5重连失败: ' + errorMsg);
                }
            } catch (error) {
                console.error('重置MT5重连失败:', error);
                ui.showAlert('danger', '重置MT5重连失败: ' + error.message);
            }
        }

        function updateTimeoutMonitorDisplay(statusData) {
            const timeoutIndicator = document.getElementById('timeoutMonitorIndicator');
            const timeoutText = document.getElementById('timeoutMonitorText');
            const timeoutInfo = document.getElementById('timeoutMonitorInfo');
            const footerTimeoutIndicator = document.getElementById('footerTimeoutMonitorIndicator');
            const footerTimeoutText = document.getElementById('footerTimeoutMonitorText');

            if (statusData.monitoring) {
                if (timeoutIndicator) timeoutIndicator.className = 'status-indicator status-online';
                if (timeoutText) timeoutText.textContent = '运行中';
                if (footerTimeoutIndicator) footerTimeoutIndicator.className = 'status-indicator status-online';
                if (footerTimeoutText) footerTimeoutText.textContent = '运行中';

                const monitoredCount = statusData.monitored_positions || 0;
                const activeCount = statusData.active_monitors ? statusData.active_monitors.length : 0;
                if (timeoutInfo) timeoutInfo.textContent = `监控${monitoredCount}个持仓 | 活跃${activeCount}个`;
            } else {
                if (timeoutIndicator) timeoutIndicator.className = 'status-indicator status-offline';
                if (timeoutText) timeoutText.textContent = '未运行';
                if (timeoutInfo) timeoutInfo.textContent = '超时监控已停止';
                if (footerTimeoutIndicator) footerTimeoutIndicator.className = 'status-indicator status-offline';
                if (footerTimeoutText) footerTimeoutText.textContent = '未运行';
            }
        }

        async function refreshSystemStatus() {
            await loadSystemStatus();
            ui.showAlert('info', '系统状态已刷新', 2000);
        }

        async function debugRefreshPositions() {
            try {
                console.log('=== 开始调试持仓显示 ===');

                // 1. 测试持仓API
                const response = await ui.apiCall('/api/positions');
                console.log('持仓API响应:', response);

                if (response && response.success) {
                    const positions = response.data || [];
                    console.log('获取到的持仓数据:', positions);
                    console.log('持仓数量:', positions.length);

                    if (positions.length > 0) {
                        positions.forEach((pos, index) => {
                            console.log(`持仓 ${index + 1}:`, {
                                ticket: pos.ticket,
                                symbol: pos.symbol,
                                volume: pos.volume,
                                profit: pos.profit,
                                type: pos.type
                            });
                        });
                    }

                    // 2. 检查页面元素
                    const openPositionsEl = document.getElementById('openPositions');
                    const profitablePositionsEl = document.getElementById('profitablePositions');
                    const lossPositionsEl = document.getElementById('lossPositions');
                    const floatingPnLEl = document.getElementById('floatingPnL');

                    console.log('页面元素检查:', {
                        openPositions: openPositionsEl ? '存在' : '不存在',
                        profitablePositions: profitablePositionsEl ? '存在' : '不存在',
                        lossPositions: lossPositionsEl ? '存在' : '不存在',
                        floatingPnL: floatingPnLEl ? '存在' : '不存在'
                    });

                    // 3. 强制更新显示
                    if (openPositionsEl) {
                        openPositionsEl.textContent = positions.length;
                        console.log('已更新持仓数量显示:', positions.length);
                    }

                    // 计算统计
                    let floatingPnL = 0;
                    let profitableCount = 0;
                    let lossCount = 0;

                    positions.forEach(pos => {
                        const profit = parseFloat(pos.profit) || 0;
                        floatingPnL += profit;

                        if (profit > 0) {
                            profitableCount++;
                        } else if (profit < 0) {
                            lossCount++;
                        }
                    });

                    console.log('计算结果:', {
                        totalPositions: positions.length,
                        profitableCount,
                        lossCount,
                        floatingPnL
                    });

                    // 更新显示
                    if (profitablePositionsEl) profitablePositionsEl.textContent = profitableCount;
                    if (lossPositionsEl) lossPositionsEl.textContent = lossCount;
                    if (floatingPnLEl) floatingPnLEl.textContent = `$${floatingPnL.toFixed(2)}`;

                    ui.showAlert('success', `调试完成！找到 ${positions.length} 个持仓`, 3000);
                } else {
                    console.error('持仓API失败:', response);
                    ui.showAlert('danger', '获取持仓数据失败');
                }

                console.log('=== 调试持仓显示结束 ===');
            } catch (error) {
                console.error('调试持仓显示异常:', error);
                ui.showAlert('danger', '调试失败: ' + error.message);
            }
        }

        // 止盈止损设置函数
        async function showSlTpModal(ticket, symbol, currentProfit) {
            try {
                // 设置基本信息
                document.getElementById('slTpTicket').value = ticket;
                document.getElementById('slTpSymbol').value = symbol;
                document.getElementById('slTpCurrentProfit').value = `$${currentProfit.toFixed(2)}`;

                // 获取当前设置
                const response = await ui.apiCall('/api/positions/sl-tp-settings');
                if (response && response.success) {
                    const settings = response.data.find(s => s.ticket === ticket);
                    if (settings) {
                        document.getElementById('slTpProfitThreshold').value = settings.profit_threshold || '';
                        document.getElementById('slTpLossThreshold').value = settings.loss_threshold || '';
                        document.getElementById('slTpEnabled').checked = settings.enabled;
                    } else {
                        // 清空表单
                        document.getElementById('slTpProfitThreshold').value = '';
                        document.getElementById('slTpLossThreshold').value = '';
                        document.getElementById('slTpEnabled').checked = true;
                    }
                }

                // 显示模态框
                const modal = new bootstrap.Modal(document.getElementById('slTpModal'));
                modal.show();

            } catch (error) {
                console.error('显示止盈止损设置失败:', error);
                ui.showAlert('danger', '获取设置失败: ' + error.message);
            }
        }

        async function saveSlTpSettings() {
            try {
                const ticket = parseInt(document.getElementById('slTpTicket').value);
                const profitThreshold = parseFloat(document.getElementById('slTpProfitThreshold').value) || null;
                const lossThreshold = parseFloat(document.getElementById('slTpLossThreshold').value) || null;
                const enabled = document.getElementById('slTpEnabled').checked;

                // 验证输入
                if (profitThreshold !== null && profitThreshold <= 0) {
                    ui.showAlert('warning', '止盈金额必须大于0');
                    return;
                }

                if (lossThreshold !== null && lossThreshold >= 0) {
                    ui.showAlert('warning', '止损金额必须小于0');
                    return;
                }

                // 发送更新请求
                const response = await ui.apiCall('/api/positions/sl-tp-settings', 'POST', {
                    ticket: ticket,
                    profit_threshold: profitThreshold,
                    loss_threshold: lossThreshold,
                    enabled: enabled
                });

                if (response && response.success) {
                    ui.showAlert('success', response.message || '止盈止损设置已保存', 3000);

                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('slTpModal'));
                    modal.hide();

                    // 刷新持仓显示
                    await ui.loadData();
                } else {
                    const errorMsg = response ? (response.error || response.message || '保存失败') : '请求失败';
                    ui.showAlert('danger', '保存设置失败: ' + errorMsg);
                }

            } catch (error) {
                console.error('保存止盈止损设置失败:', error);
                ui.showAlert('danger', '保存设置失败: ' + error.message);
            }
        }

        // 全局止盈止损设置函数
        async function showGlobalSlTpModal() {
            try {
                // 获取当前全局设置
                const response = await ui.apiCall('/api/positions/global-sl-tp');
                if (response && response.success) {
                    const settings = response.data;
                    document.getElementById('globalProfitThreshold').value = settings.profit_threshold || '';
                    document.getElementById('globalLossThreshold').value = settings.loss_threshold || '';
                    document.getElementById('globalSlTpEnabled').checked = settings.enabled;
                } else {
                    // 清空表单
                    document.getElementById('globalProfitThreshold').value = '';
                    document.getElementById('globalLossThreshold').value = '';
                    document.getElementById('globalSlTpEnabled').checked = false;
                }

                // 显示模态框
                const modal = new bootstrap.Modal(document.getElementById('globalSlTpModal'));
                modal.show();

            } catch (error) {
                console.error('显示全局止盈止损设置失败:', error);
                ui.showAlert('danger', '获取全局设置失败: ' + error.message);
            }
        }

        async function saveGlobalSlTpSettings() {
            try {
                const profitThreshold = parseFloat(document.getElementById('globalProfitThreshold').value) || null;
                const lossThreshold = parseFloat(document.getElementById('globalLossThreshold').value) || null;
                const enabled = document.getElementById('globalSlTpEnabled').checked;

                // 验证输入
                if (profitThreshold !== null && profitThreshold <= 0) {
                    ui.showAlert('warning', '全局止盈金额必须大于0');
                    return;
                }

                if (lossThreshold !== null && lossThreshold >= 0) {
                    ui.showAlert('warning', '全局止损金额必须小于0');
                    return;
                }

                // 发送更新请求
                const response = await ui.apiCall('/api/positions/global-sl-tp', 'POST', {
                    profit_threshold: profitThreshold,
                    loss_threshold: lossThreshold,
                    enabled: enabled
                });

                if (response && response.success) {
                    ui.showAlert('success', response.message || '全局止盈止损设置已保存', 3000);

                    // 关闭模态框
                    const modal = bootstrap.Modal.getInstance(document.getElementById('globalSlTpModal'));
                    modal.hide();

                    // 刷新持仓显示
                    await ui.loadData();
                } else {
                    const errorMsg = response ? (response.error || response.message || '保存失败') : '请求失败';
                    ui.showAlert('danger', '保存全局设置失败: ' + errorMsg);
                }

            } catch (error) {
                console.error('保存全局止盈止损设置失败:', error);
                ui.showAlert('danger', '保存全局设置失败: ' + error.message);
            }
        }

        // 其他功能函数
        function showConfigModal() {
            showSystemConfigModal();
        }

        async function startStrategy() {
            try {
                const response = await ui.apiCall('/api/strategy/start', 'POST');
                if (response && response.success) {
                    ui.showAlert('success', '策略启动成功');
                    ui.loadData();
                } else {
                    ui.showAlert('danger', '策略启动失败');
                }
            } catch (error) {
                ui.showAlert('danger', '策略启动失败: ' + error.message);
            }
        }

        async function stopStrategy() {
            try {
                const response = await ui.apiCall('/api/strategy/stop', 'POST');
                if (response && response.success) {
                    ui.showAlert('success', '策略停止成功');
                    ui.loadData();
                } else {
                    ui.showAlert('danger', '策略停止失败');
                }
            } catch (error) {
                ui.showAlert('danger', '策略停止失败: ' + error.message);
            }
        }

        async function emergencyStop() {
            if (confirm('确定要执行紧急停止吗？这将关闭所有持仓并停止策略。')) {
                try {
                    const response = await ui.apiCall('/api/strategy/emergency-stop', 'POST');
                    if (response && response.success) {
                        ui.showAlert('warning', '紧急停止执行成功');
                        ui.loadData();
                        showPage('positions');
                    } else {
                        ui.showAlert('danger', '紧急停止失败');
                    }
                } catch (error) {
                    ui.showAlert('danger', '紧急停止失败: ' + error.message);
                }
            }
        }

        async function closePosition(ticket) {
            if (confirm(`确定要平仓订单 ${ticket} 吗？`)) {
                try {
                    const response = await ui.apiCall(`/api/positions/${ticket}/close`, 'POST', {
                        reason: '手动平仓'
                    });
                    if (response && response.success) {
                        ui.showAlert('success', '平仓成功');
                        loadPositionsData();
                        ui.loadData();
                    } else {
                        ui.showAlert('danger', '平仓失败');
                    }
                } catch (error) {
                    ui.showAlert('danger', '平仓失败: ' + error.message);
                }
            }
        }

        async function closeAllPositions() {
            if (!confirm('⚠️ 确定要平仓所有持仓吗？\n\n此操作将关闭所有交易对的所有持仓，且不可撤销。')) {
                return;
            }

            try {
                const response = await ui.apiCall('/api/positions/close-all', 'POST');
                if (response && response.success) {
                    ui.showAlert('success', response.message || '所有持仓平仓成功', 3000);
                    loadPositionsData();
                    ui.loadData();
                } else {
                    ui.showAlert('danger', '平仓失败: ' + (response.error || '未知错误'));
                }
            } catch (error) {
                ui.showAlert('danger', '平仓失败: ' + error.message);
            }
        }

        async function closeProfitablePositions() {
            if (!confirm('💰 确定要平仓所有盈利订单吗？\n\n此操作将关闭所有当前盈利的持仓，锁定盈利。')) {
                return;
            }

            try {
                const response = await ui.apiCall('/api/positions/close-profitable', 'POST');
                if (response && response.success) {
                    let message = response.message || '盈利订单平仓成功';
                    if (response.total_profit) {
                        message += ` (总盈利: $${response.total_profit.toFixed(2)})`;
                    }
                    ui.showAlert('success', message, 3000);
                    loadPositionsData();
                    ui.loadData();
                } else {
                    ui.showAlert('danger', '平仓盈利订单失败: ' + (response.error || '未知错误'));
                }
            } catch (error) {
                ui.showAlert('danger', '平仓盈利订单失败: ' + error.message);
            }
        }

        async function closeLossPositions() {
            if (!confirm('📉 确定要平仓所有亏损订单吗？\n\n此操作将关闭所有当前亏损的持仓，止损离场。')) {
                return;
            }

            try {
                const response = await ui.apiCall('/api/positions/close-loss', 'POST');
                if (response && response.success) {
                    let message = response.message || '亏损订单平仓成功';
                    if (response.total_loss) {
                        message += ` (总亏损: $${response.total_loss.toFixed(2)})`;
                    }
                    ui.showAlert('success', message, 3000);
                    loadPositionsData();
                    ui.loadData();
                } else {
                    ui.showAlert('danger', '平仓亏损订单失败: ' + (response.error || '未知错误'));
                }
            } catch (error) {
                ui.showAlert('danger', '平仓亏损订单失败: ' + error.message);
            }
        }

        async function showPositionStats() {
            try {
                const response = await ui.apiCall('/api/positions', 'GET');
                if (response && response.success) {
                    const positions = response.data || [];

                    if (positions.length === 0) {
                        ui.showAlert('info', '当前没有持仓', 2000);
                        return;
                    }

                    // 计算统计信息
                    const totalPositions = positions.length;
                    const profitableCount = positions.filter(p => (p.profit || 0) > 0).length;
                    const lossCount = positions.filter(p => (p.profit || 0) < 0).length;
                    const breakEvenCount = totalPositions - profitableCount - lossCount;
                    const totalProfit = positions.reduce((sum, p) => sum + (p.profit || 0), 0);
                    const totalVolume = positions.reduce((sum, p) => sum + (p.volume || 0), 0);

                    // 计算平均盈亏
                    const avgProfit = totalPositions > 0 ? totalProfit / totalPositions : 0;

                    const statsMessage = `📊 持仓统计信息

🔢 总持仓数: ${totalPositions}
💰 盈利订单: ${profitableCount}
📉 亏损订单: ${lossCount}
⚖️ 持平订单: ${breakEvenCount}
💵 总盈亏: $${totalProfit.toFixed(2)}
📊 平均盈亏: $${avgProfit.toFixed(2)}
📈 总手数: ${totalVolume.toFixed(2)}`;

                    alert(statsMessage);
                } else {
                    const errorMsg = response ? (response.error || response.message || '未知错误') : '请求失败';
                    ui.showAlert('danger', '获取持仓统计失败: ' + errorMsg);
                    console.error('持仓统计API错误:', response);
                }
            } catch (error) {
                console.error('持仓统计异常:', error);
                ui.showAlert('danger', '获取持仓统计失败: ' + error.message);
            }
        }

        // 交易对操作函数
        function enableAllSymbols() {
            const checkboxes = document.querySelectorAll('#symbolsConfigBody .symbol-enabled');
            let count = 0;
            checkboxes.forEach(cb => {
                if (!cb.checked) {
                    cb.checked = true;
                    cb.onchange();
                    count++;
                }
            });
            if (count > 0) {
                ui.showAlert('success', `已启用 ${count} 个交易对`, 2000);
            }
        }

        function disableAllSymbols() {
            const checkboxes = document.querySelectorAll('#symbolsConfigBody .symbol-enabled');
            let count = 0;
            checkboxes.forEach(cb => {
                if (cb.checked) {
                    cb.checked = false;
                    cb.onchange();
                    count++;
                }
            });
            if (count > 0) {
                ui.showAlert('warning', `已禁用 ${count} 个交易对`, 2000);
            }
        }

        async function editSymbolConfig(symbol) {
            // 获取当前配置
            const response = await ui.apiCall(`/api/config/symbols/${symbol}`);
            if (response && response.success) {
                const config = response.data;
                showSymbolEditModal(symbol, config);
            }
        }

        async function resetSymbolConfig(symbol) {
            if (confirm(`确定要重置 ${symbol} 的配置为默认值吗？`)) {
                try {
                    const defaultConfig = {
                        enabled: false,
                        lot_size: 0.01,
                        stop_loss_usd: 50,
                        take_profit_usd: 100,
                        signal_timeout_seconds: 180
                    };

                    const response = await ui.apiCall(`/api/config/symbols/${symbol}`, 'PUT', defaultConfig);
                    if (response && response.success) {
                        ui.showAlert('success', `${symbol} 配置已重置`, 2000);
                        await loadSymbolConfigs();
                    } else {
                        ui.showAlert('danger', '重置配置失败');
                    }
                } catch (error) {
                    ui.showAlert('danger', '重置配置失败: ' + error.message);
                }
            }
        }

        function showSymbolEditModal(symbol, config) {
            const modalHtml = `
                <div class="modal fade" id="symbolEditModal" tabindex="-1">
                    <div class="modal-dialog">
                        <div class="modal-content">
                            <div class="modal-header">
                                <h5 class="modal-title">编辑 ${symbol} 配置</h5>
                                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                            </div>
                            <div class="modal-body">
                                <form id="symbolEditForm">
                                    <div class="mb-3">
                                        <div class="form-check">
                                            <input class="form-check-input" type="checkbox" id="editEnabled"
                                                   ${config.enabled ? 'checked' : ''}>
                                            <label class="form-check-label" for="editEnabled">启用交易</label>
                                        </div>
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">手数</label>
                                        <input type="number" class="form-control" id="editLotSize"
                                               value="${config.lot_size}" step="0.01" min="0.01">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">止损 (USD)</label>
                                        <input type="number" class="form-control" id="editStopLoss"
                                               value="${config.stop_loss_usd}" step="1" min="1">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">止盈 (USD)</label>
                                        <input type="number" class="form-control" id="editTakeProfit"
                                               value="${config.take_profit_usd}" step="1" min="1">
                                    </div>
                                    <div class="mb-3">
                                        <label class="form-label">信号超时 (秒)</label>
                                        <input type="number" class="form-control" id="editTimeout"
                                               value="${config.signal_timeout_seconds}" step="30" min="30">
                                    </div>
                                </form>
                            </div>
                            <div class="modal-footer">
                                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                                <button type="button" class="btn btn-primary" onclick="saveSymbolEdit('${symbol}')">保存</button>
                            </div>
                        </div>
                    </div>
                </div>
            `;

            // 移除旧模态框
            const oldModal = document.getElementById('symbolEditModal');
            if (oldModal) oldModal.remove();

            // 添加新模态框
            document.body.insertAdjacentHTML('beforeend', modalHtml);

            // 显示模态框
            const modal = new bootstrap.Modal(document.getElementById('symbolEditModal'));
            modal.show();
        }

        async function saveSymbolEdit(symbol) {
            try {
                const config = {
                    enabled: document.getElementById('editEnabled').checked,
                    lot_size: parseFloat(document.getElementById('editLotSize').value),
                    stop_loss_usd: parseInt(document.getElementById('editStopLoss').value),
                    take_profit_usd: parseInt(document.getElementById('editTakeProfit').value),
                    signal_timeout_seconds: parseInt(document.getElementById('editTimeout').value)
                };

                const response = await ui.apiCall(`/api/config/symbols/${symbol}`, 'PUT', config);
                if (response && response.success) {
                    ui.showAlert('success', `${symbol} 配置保存成功`, 2000);
                    const modal = bootstrap.Modal.getInstance(document.getElementById('symbolEditModal'));
                    modal.hide();
                    await loadSymbolConfigs();
                } else {
                    ui.showAlert('danger', '保存配置失败');
                }
            } catch (error) {
                ui.showAlert('danger', '保存配置失败: ' + error.message);
            }
        }

        // 简单的提示函数
        function showAlert(type, message, duration = 3000) {
            // 创建提示元素
            const alertDiv = document.createElement('div');
            alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
            alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            alertDiv.innerHTML = `
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            `;

            document.body.appendChild(alertDiv);

            // 自动移除
            setTimeout(() => {
                if (alertDiv.parentNode) {
                    alertDiv.parentNode.removeChild(alertDiv);
                }
            }, duration);
        }

        // 获取同步状态徽章
        function getSyncStatusBadge(lastSyncTime) {
            if (!lastSyncTime) {
                return '<span class="badge bg-warning">未同步</span>';
            }

            const syncTime = new Date(lastSyncTime);
            const now = new Date();
            const diffMinutes = (now - syncTime) / (1000 * 60);

            if (diffMinutes < 5) {
                return '<span class="badge bg-success">已同步</span>';
            } else {
                return '<span class="badge bg-danger">需同步</span>';
            }
        }

        // 渲染分页
        function renderTradesPagination(pagination) {
            const info = document.getElementById('tradesInfo');
            const paginationEl = document.getElementById('tradesPagination');

            if (!info || !paginationEl) return;

            const { page = 1, pages = 1, total = 0 } = pagination;

            // 显示信息
            info.textContent = `共 ${total} 条记录，第 ${page} / ${pages} 页`;

            // 生成分页按钮
            let paginationHTML = '';

            if (page > 1) {
                paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="changeTradesPage(${page - 1})">上一页</a></li>`;
            }

            for (let i = Math.max(1, page - 2); i <= Math.min(pages, page + 2); i++) {
                paginationHTML += `<li class="page-item ${i === page ? 'active' : ''}">
                    <a class="page-link" href="#" onclick="changeTradesPage(${i})">${i}</a>
                </li>`;
            }

            if (page < pages) {
                paginationHTML += `<li class="page-item"><a class="page-link" href="#" onclick="changeTradesPage(${page + 1})">下一页</a></li>`;
            }

            paginationEl.innerHTML = paginationHTML;
        }

        // 切换页面
        function changeTradesPage(page) {
            currentTradesPage = page;
            loadTradesData();
        }

        // 应用筛选
        function applyTradesFilter() {
            tradesFilters.status = document.getElementById('statusFilter')?.value || '';
            tradesFilters.symbol = document.getElementById('symbolFilter')?.value || '';
            tradesFilters.limit = parseInt(document.getElementById('limitFilter')?.value || 20);
            currentTradesPage = 1;
            loadTradesData();
        }

        // 同步所有订单
        async function syncAllOrders() {
            try {
                showAlert('info', '正在同步所有订单...', 3000);

                const response = await fetch('/api/trades/sync', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({})
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data && data.success) {
                        showAlert('success', data.message || '同步成功', 3000);
                        setTimeout(() => loadTradesData(), 2000); // 2秒后刷新数据
                    } else {
                        // 显示具体的错误信息或消息
                        const errorMsg = data?.error || data?.message || '未知错误';
                        showAlert('warning', '同步信息: ' + errorMsg);
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                console.error('同步失败:', error);
                showAlert('danger', '同步失败: ' + error.message);
            }
        }

        // 同步单个订单
        async function syncSingleOrder(ticket) {
            if (!ticket) {
                showAlert('warning', '订单号无效');
                return;
            }

            try {
                showAlert('info', `正在同步订单 #${ticket}...`, 2000);

                const response = await fetch('/api/trades/sync', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({ mt5_ticket: ticket })
                });

                if (response.ok) {
                    const data = await response.json();
                    if (data && data.success) {
                        showAlert('success', data.message || '同步成功', 2000);
                        setTimeout(() => loadTradesData(), 1000); // 1秒后刷新数据
                    } else {
                        // 显示具体的错误信息或消息
                        const errorMsg = data?.error || data?.message || '未知错误';
                        showAlert('info', '同步信息: ' + errorMsg);
                    }
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            } catch (error) {
                console.error('同步失败:', error);
                showAlert('danger', '同步失败: ' + error.message);
            }
        }

        function refreshTrades() {
            loadTradesData();
        }

        function refreshPositions() {
            loadPositionsData();
        }

        function showSystemSettings() {
            showPage('config');
        }

        function showRiskConfigModal() {
            ui.showAlert('info', '风险控制配置功能开发中...');
        }

        function showNotificationConfig() {
            ui.showAlert('info', '通知配置功能开发中...');
        }

        // 交易时段管理
        class TradingSessionManager {
            constructor() {
                this.sessions = {
                    sydney: { name: '悉尼', flag: '🇦🇺', start: 6, end: 15, timezone: 'Australia/Sydney' },
                    tokyo: { name: '东京', flag: '🇯🇵', start: 8, end: 17, timezone: 'Asia/Tokyo' },
                    london: { name: '伦敦', flag: '🇬🇧', start: 16, end: 1, timezone: 'Europe/London' },
                    newyork: { name: '纽约', flag: '🇺🇸', start: 21, end: 6, timezone: 'America/New_York' }
                };
                this.updateInterval = null;
            }

            init() {
                this.updateTradingSessions();
                this.updateInterval = setInterval(() => {
                    this.updateTradingSessions();
                }, 1000); // 每秒更新
            }

            updateTradingSessions() {
                const now = new Date();
                const currentHour = now.getHours();

                // 更新当前时间显示
                document.getElementById('currentTime').textContent =
                    now.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit'
                    });

                // 检查当前活跃市场
                const activeSessions = this.getActiveSessions(currentHour);
                const nextSession = this.getNextSession(currentHour);

                this.updateActiveMarket(activeSessions);
                this.updateNextMarket(nextSession, now);
                this.updateMarketSchedule(currentHour);
            }

            getActiveSessions(currentHour) {
                const active = [];

                for (const [key, session] of Object.entries(this.sessions)) {
                    if (this.isSessionActive(session, currentHour)) {
                        active.push({ key, ...session });
                    }
                }

                return active;
            }

            isSessionActive(session, currentHour) {
                if (session.start < session.end) {
                    // 同一天内的时段
                    return currentHour >= session.start && currentHour < session.end;
                } else {
                    // 跨天的时段
                    return currentHour >= session.start || currentHour < session.end;
                }
            }

            getNextSession(currentHour) {
                let nextSession = null;
                let minHoursToNext = 24;

                for (const [key, session] of Object.entries(this.sessions)) {
                    let hoursToNext;

                    if (session.start >= currentHour) {
                        hoursToNext = session.start - currentHour;
                    } else {
                        hoursToNext = 24 - currentHour + session.start;
                    }

                    if (hoursToNext < minHoursToNext && !this.isSessionActive(session, currentHour)) {
                        minHoursToNext = hoursToNext;
                        nextSession = { key, ...session, hoursToNext };
                    }
                }

                return nextSession;
            }

            updateActiveMarket(activeSessions) {
                const nameEl = document.getElementById('activeMarketName');
                const timeEl = document.getElementById('activeMarketTime');
                const statusEl = document.getElementById('currentMarketStatus');

                if (activeSessions.length === 0) {
                    nameEl.textContent = '市场休市';
                    timeEl.textContent = '等待下一个交易时段';
                    statusEl.textContent = '休市';
                    statusEl.className = 'badge bg-secondary';
                } else if (activeSessions.length === 1) {
                    const session = activeSessions[0];
                    nameEl.textContent = `${session.flag} ${session.name}市场`;
                    timeEl.textContent = `${session.start}:00 - ${session.end}:00`;
                    statusEl.textContent = '交易中';
                    statusEl.className = 'badge bg-success';
                } else {
                    // 多个市场重叠
                    const sessionNames = activeSessions.map(s => `${s.flag}${s.name}`).join(' & ');
                    nameEl.textContent = sessionNames;
                    timeEl.textContent = '重叠交易时段';
                    statusEl.textContent = '高活跃';
                    statusEl.className = 'badge bg-warning text-dark';
                }
            }

            updateNextMarket(nextSession, now) {
                const nameEl = document.getElementById('nextMarketName');
                const countdownEl = document.getElementById('marketCountdown');
                const statusEl = document.getElementById('nextMarketStatus');

                if (!nextSession) {
                    nameEl.textContent = '计算中...';
                    countdownEl.textContent = '--:--:--';
                    statusEl.textContent = '等待';
                    return;
                }

                // 计算到下一个市场开盘的时间
                const nextOpenTime = new Date(now);
                nextOpenTime.setHours(nextSession.start, 0, 0, 0);

                if (nextSession.start <= now.getHours()) {
                    nextOpenTime.setDate(nextOpenTime.getDate() + 1);
                }

                const timeDiff = nextOpenTime - now;
                const hours = Math.floor(timeDiff / (1000 * 60 * 60));
                const minutes = Math.floor((timeDiff % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((timeDiff % (1000 * 60)) / 1000);

                nameEl.textContent = `${nextSession.flag} ${nextSession.name}市场`;

                // 根据剩余时间调整显示颜色
                let countdownClass = 'countdown-inline';
                if (hours === 0 && minutes < 30) {
                    countdownClass += ' countdown-urgent';
                } else if (hours < 2) {
                    countdownClass += ' countdown-soon';
                }

                countdownEl.className = countdownClass;
                countdownEl.textContent = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

                // 根据时间调整状态文本和样式
                if (hours === 0 && minutes < 30) {
                    statusEl.textContent = '即将开盘';
                    statusEl.className = 'badge bg-danger';
                } else if (hours < 2) {
                    statusEl.textContent = '准备中';
                    statusEl.className = 'badge bg-warning text-dark';
                } else {
                    statusEl.textContent = '等待';
                    statusEl.className = 'badge bg-secondary';
                }
            }

            updateMarketSchedule(currentHour) {
                for (const [key, session] of Object.entries(this.sessions)) {
                    const statusEl = document.getElementById(`${key}-status`);

                    if (this.isSessionActive(session, currentHour)) {
                        statusEl.textContent = '●';
                        statusEl.className = 'market-status active';
                    } else {
                        statusEl.textContent = '○';
                        statusEl.className = 'market-status inactive';
                    }
                }
            }

            destroy() {
                if (this.updateInterval) {
                    clearInterval(this.updateInterval);
                }
            }
        }

        // 全局交易时段管理器
        let tradingSessionManager;

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            ui = new SimpleUI();

            // 初始化底部栏显示状态（默认显示首页）
            const footer = document.getElementById('systemStatusFooter');
            const body = document.body;
            footer.style.display = 'block';
            body.classList.add('has-footer');

            // 初始化交易时段管理器
            tradingSessionManager = new TradingSessionManager();

            // 添加主题切换事件监听
            const themeSelector = document.getElementById('uiTheme');
            if (themeSelector) {
                themeSelector.addEventListener('change', function() {
                    ui.saveThemePreference(this.value);
                });
            }
            tradingSessionManager.init();

            // 移动端表格滚动优化初始化
            if (window.innerWidth <= 768) {
                setTimeout(optimizeMobileTableScroll, 500);
                setTimeout(optimizeMobileTableScroll, 2000);
                setTimeout(optimizeMobileTableScroll, 5000);
            }

            // 启动价格更新
            setTimeout(() => {
                startPriceUpdates();
                loadSystemStatus();
            }, 2000); // 延迟2秒启动，确保页面完全加载

            // 定时刷新系统状态（每30秒）
            setInterval(() => {
                loadSystemStatus();
            }, 30000);

            // 定时刷新账户信息和持仓数据（每10秒）
            setInterval(() => {
                if (ui) {
                    ui.loadAccountInfo();
                    // 如果当前在持仓页面，也刷新持仓数据
                    const currentPage = document.querySelector('.page-content[style*="block"]');
                    if (currentPage && currentPage.id === 'positionsPage') {
                        loadPositionsData();
                    }
                }
            }, 10000);

            // 定时刷新警报统计和最新警报（每30秒）
            setInterval(() => {
                if (ui) {
                    ui.loadAlertStats();
                    ui.loadRecentAlerts();
                }
            }, 30000);

            // 监听窗口大小改变，重新优化表格
            window.addEventListener('resize', () => {
                setTimeout(() => {
                    if (window.innerWidth <= 768) {
                        optimizeMobileTableScroll();
                    }
                }, 300);
            });
        });

        // 页面卸载时清理资源
        window.addEventListener('beforeunload', function() {
            if (tradingSessionManager) {
                tradingSessionManager.destroy();
            }
        });
    </script>

    <!-- 止盈止损设置模态框 -->
    <div class="modal fade" id="slTpModal" tabindex="-1" aria-labelledby="slTpModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="slTpModalLabel">
                        <i class="bi bi-gear"></i> 止盈止损设置
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="slTpForm">
                        <div class="row mb-3">
                            <div class="col-6">
                                <label class="form-label">订单号</label>
                                <input type="text" class="form-control" id="slTpTicket" readonly>
                            </div>
                            <div class="col-6">
                                <label class="form-label">交易对</label>
                                <input type="text" class="form-control" id="slTpSymbol" readonly>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-12">
                                <label class="form-label">当前盈亏</label>
                                <input type="text" class="form-control" id="slTpCurrentProfit" readonly>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-6">
                                <label for="slTpProfitThreshold" class="form-label">
                                    <i class="bi bi-arrow-up-circle text-success"></i> 止盈金额 ($)
                                </label>
                                <input type="number" class="form-control" id="slTpProfitThreshold"
                                       step="0.01" min="0" placeholder="例如: 10.00">
                                <div class="form-text">达到此盈利金额时自动平仓</div>
                            </div>
                            <div class="col-6">
                                <label for="slTpLossThreshold" class="form-label">
                                    <i class="bi bi-arrow-down-circle text-danger"></i> 止损金额 ($)
                                </label>
                                <input type="number" class="form-control" id="slTpLossThreshold"
                                       step="0.01" max="0" placeholder="例如: -5.00">
                                <div class="form-text">达到此亏损金额时自动平仓</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-12">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="slTpEnabled" checked>
                                    <label class="form-check-label" for="slTpEnabled">
                                        启用止盈止损监控
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            <strong>说明：</strong>
                            <ul class="mb-0 mt-2">
                                <li>止盈金额为正数，止损金额为负数</li>
                                <li>设置后立即生效，系统会自动监控</li>
                                <li>留空表示不设置该项限制</li>
                            </ul>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveSlTpSettings()">
                        <i class="bi bi-check"></i> 保存设置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- 全局止盈止损设置模态框 -->
    <div class="modal fade" id="globalSlTpModal" tabindex="-1" aria-labelledby="globalSlTpModalLabel" aria-hidden="true">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="globalSlTpModalLabel">
                        <i class="bi bi-gear-wide-connected"></i> 总持仓盈亏监控设置
                    </h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <form id="globalSlTpForm">
                        <div class="row mb-3">
                            <div class="col-12">
                                <div class="alert alert-info">
                                    <i class="bi bi-info-circle"></i>
                                    <strong>总持仓盈亏监控：</strong>监控所有订单的盈亏总和（包括系统下单和手动下单），达到阈值时平仓所有订单
                                </div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-6">
                                <label for="globalProfitThreshold" class="form-label">
                                    <i class="bi bi-arrow-up-circle text-success"></i> 总盈利阈值 ($)
                                </label>
                                <input type="number" class="form-control" id="globalProfitThreshold"
                                       step="0.01" min="0" placeholder="例如: 100.00">
                                <div class="form-text">所有订单盈亏总和达到此金额时平仓所有订单</div>
                            </div>
                            <div class="col-6">
                                <label for="globalLossThreshold" class="form-label">
                                    <i class="bi bi-arrow-down-circle text-danger"></i> 总亏损阈值 ($)
                                </label>
                                <input type="number" class="form-control" id="globalLossThreshold"
                                       step="0.01" max="0" placeholder="例如: -50.00">
                                <div class="form-text">所有订单盈亏总和达到此金额时平仓所有订单</div>
                            </div>
                        </div>

                        <div class="row mb-3">
                            <div class="col-12">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="globalSlTpEnabled">
                                    <label class="form-check-label" for="globalSlTpEnabled">
                                        启用总持仓盈亏监控
                                    </label>
                                </div>
                            </div>
                        </div>

                        <div class="alert alert-info">
                            <i class="bi bi-info-circle"></i>
                            <strong>说明：</strong>
                            <ul class="mb-0 mt-2">
                                <li><strong>监控范围</strong>：所有订单（系统下单 + 手动下单）</li>
                                <li><strong>计算方式</strong>：所有订单盈亏的总和</li>
                                <li><strong>触发条件</strong>：总盈亏达到阈值时平仓所有订单</li>
                                <li><strong>优先级高</strong>：优先于单个订单的止盈止损</li>
                                <li><strong>风险控制</strong>：防止总体亏损过大或锁定总体盈利</li>
                                <li>留空表示不设置该项限制</li>
                            </ul>
                        </div>

                        <div class="alert alert-success">
                            <i class="bi bi-lightbulb"></i>
                            <strong>示例：</strong>
                            <p class="mb-1">假设您有3个订单：</p>
                            <ul class="mb-2">
                                <li>订单A：+$30</li>
                                <li>订单B：-$10</li>
                                <li>订单C：+$50</li>
                            </ul>
                            <p class="mb-0">总盈亏 = $30 + (-$10) + $50 = <strong>$70</strong></p>
                            <p class="mb-0">如果设置总盈利阈值为$60，则会平仓所有3个订单</p>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                    <button type="button" class="btn btn-primary" onclick="saveGlobalSlTpSettings()">
                        <i class="bi bi-check"></i> 保存全局设置
                    </button>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 警报原始JSON数据相关函数
        async function viewAlertRawData(alertId) {
            try {
                // 显示加载状态
                const modal = new bootstrap.Modal(document.getElementById('alertRawDataModal'));
                document.getElementById('alertRawDataId').textContent = alertId;
                document.getElementById('alertRawDataContent').textContent = '加载中...';
                modal.show();

                // 获取原始数据
                const response = await ui.apiCall(`/api/alerts/${alertId}/raw`);

                if (response && response.success && response.data) {
                    const rawData = response.data.raw_data;

                    // 格式化JSON显示
                    const formattedJson = JSON.stringify(rawData, null, 2);
                    document.getElementById('alertRawDataContent').textContent = formattedJson;
                } else {
                    document.getElementById('alertRawDataContent').textContent = '获取数据失败: ' + (response?.error || '未知错误');
                }
            } catch (error) {
                console.error('获取警报原始数据失败:', error);
                document.getElementById('alertRawDataContent').textContent = '获取数据失败: ' + error.message;
            }
        }

        function copyAlertRawData() {
            const content = document.getElementById('alertRawDataContent').textContent;

            if (content && content !== '加载中...' && !content.startsWith('获取数据失败')) {
                navigator.clipboard.writeText(content).then(() => {
                    // 显示复制成功提示
                    ui.showAlert('success', 'JSON数据已复制到剪贴板');
                }).catch(err => {
                    console.error('复制失败:', err);
                    ui.showAlert('error', '复制失败，请手动选择复制');
                });
            } else {
                ui.showAlert('warning', '没有可复制的数据');
            }
        }

        // 监控相关函数
        let monitoringTimer = null;

        async function loadMonitoringStatus() {
            try {
                const response = await ui.apiCall('/api/monitoring/status');
                updateMonitoringStatus(response.data);
            } catch (error) {
                console.error('加载监控状态失败:', error);
            }
        }

        function updateMonitoringStatus(status) {
            const statusElement = document.getElementById('monitoringStatus');
            const toggleBtn = document.getElementById('toggleMonitoringBtn');
            const countElement = document.getElementById('monitoredCount');

            if (!statusElement || !toggleBtn || !countElement) return;

            if (status.is_running) {
                statusElement.className = 'badge bg-success';
                statusElement.innerHTML = '<i class="bi bi-shield-check"></i> 运行中';
                toggleBtn.innerHTML = '<i class="bi bi-pause-circle"></i> 暂停监控';
                toggleBtn.className = 'btn btn-sm btn-outline-warning ms-3';
            } else {
                statusElement.className = 'badge bg-danger';
                statusElement.innerHTML = '<i class="bi bi-shield-x"></i> 已停止';
                toggleBtn.innerHTML = '<i class="bi bi-play-circle"></i> 启动监控';
                toggleBtn.className = 'btn btn-sm btn-outline-primary ms-3';
            }

            countElement.textContent = status.monitored_positions || 0;
        }

        async function toggleMonitoring() {
            try {
                const response = await ui.apiCall('/api/monitoring/toggle', 'POST');
                updateMonitoringStatus({
                    is_running: response.data.is_running,
                    monitored_positions: 0
                });

                ui.showAlert('success', response.data.action === 'started' ? '监控已启动' : '监控已暂停');

                // 刷新持仓数据
                loadPositionsData();
            } catch (error) {
                console.error('切换监控状态失败:', error);
                ui.showAlert('error', '操作失败: ' + error.message);
            }
        }

        async function addPositionMonitoring(ticket) {
            try {
                const timeoutSeconds = prompt('请输入超时时间（秒）:', '180');
                if (!timeoutSeconds || isNaN(timeoutSeconds)) {
                    return;
                }

                const response = await ui.apiCall(`/api/positions/${ticket}/monitoring`, 'POST', {
                    timeout_seconds: parseInt(timeoutSeconds)
                });

                ui.showAlert('success', response.message);
                loadPositionsData();
                loadMonitoringStatus();
            } catch (error) {
                console.error('添加监控失败:', error);
                ui.showAlert('error', '添加监控失败: ' + error.message);
            }
        }

        async function removePositionMonitoring(ticket) {
            try {
                if (!confirm(`确定要停止订单#${ticket}的监控吗？`)) {
                    return;
                }

                const response = await ui.apiCall(`/api/positions/${ticket}/monitoring`, 'DELETE');

                ui.showAlert('success', response.message);
                loadPositionsData();
                loadMonitoringStatus();
            } catch (error) {
                console.error('移除监控失败:', error);
                ui.showAlert('error', '移除监控失败: ' + error.message);
            }
        }

        async function loadMonitoringInfo() {
            try {
                const response = await ui.apiCall('/api/positions');
                if (response.success && response.data) {
                    updateMonitoringDisplay(response.data);
                    startCountdownTimer();
                }
            } catch (error) {
                console.error('加载监控信息失败:', error);
            }
        }

        function updateMonitoringDisplay(positions) {
            positions.forEach(position => {
                const ticket = position.ticket;
                const monitoring = position.monitoring || { enabled: false, remaining_seconds: 0 };

                const countdownElement = document.getElementById(`countdown-${ticket}`);
                const addButton = document.getElementById(`add-monitor-btn-${ticket}`);
                const removeButton = document.getElementById(`remove-monitor-btn-${ticket}`);

                if (countdownElement && addButton && removeButton) {
                    if (monitoring.enabled) {
                        // 显示倒计时和移除按钮
                        updateCountdownDisplay(countdownElement, monitoring.remaining_seconds);
                        addButton.style.display = 'none';
                        removeButton.style.display = 'inline-block';
                    } else {
                        // 显示未监控状态和添加按钮
                        countdownElement.className = 'badge bg-secondary me-2';
                        countdownElement.textContent = '未监控';
                        addButton.style.display = 'inline-block';
                        removeButton.style.display = 'none';
                    }
                }
            });
        }

        function updateCountdownDisplay(element, seconds) {
            if (seconds <= 0) {
                element.className = 'badge bg-danger me-2';
                element.innerHTML = '<i class="bi bi-exclamation-triangle"></i> 已超时';
                return;
            }

            const minutes = Math.floor(seconds / 60);
            const remainingSeconds = seconds % 60;
            const timeStr = `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;

            let badgeClass = 'bg-success';
            if (seconds <= 30) {
                badgeClass = 'bg-danger';
            } else if (seconds <= 60) {
                badgeClass = 'bg-warning';
            }

            element.className = `badge ${badgeClass} me-2`;
            element.innerHTML = `<i class="bi bi-clock"></i> ${timeStr}`;
            element.dataset.remaining = seconds;
        }

        function startCountdownTimer() {
            if (monitoringTimer) {
                clearInterval(monitoringTimer);
            }

            monitoringTimer = setInterval(() => {
                const countdownElements = document.querySelectorAll('[data-remaining]');
                countdownElements.forEach(element => {
                    const remaining = parseInt(element.dataset.remaining) - 1;
                    element.dataset.remaining = remaining;
                    updateCountdownDisplay(element, remaining);
                });
            }, 1000);
        }

        // 保护止盈相关函数
        function showProtectiveProfitModal(ticket, currentProfit) {
            if (currentProfit <= 0) {
                ui.showAlert('warning', '只有盈利的订单才能设置保护止盈');
                return;
            }

            const protectiveLevel = prompt(
                `订单#${ticket}当前盈利: $${currentProfit.toFixed(2)}\n\n请输入保护止盈水平（美元）:`,
                Math.max(0, currentProfit * 0.5).toFixed(2)
            );

            if (protectiveLevel === null) return;

            const level = parseFloat(protectiveLevel);
            if (isNaN(level)) {
                ui.showAlert('error', '请输入有效的数字');
                return;
            }

            if (level > currentProfit) {
                ui.showAlert('error', `保护水平($${level})不能高于当前盈利($${currentProfit.toFixed(2)})`);
                return;
            }

            addProtectiveProfit(ticket, level);
        }

        async function addProtectiveProfit(ticket, protectiveLevel) {
            try {
                const response = await ui.apiCall(`/api/positions/${ticket}/protective-profit`, 'POST', {
                    protective_level: protectiveLevel
                });

                ui.showAlert('success', response.message);
                loadPositionsData();
            } catch (error) {
                console.error('设置保护止盈失败:', error);
                ui.showAlert('error', '设置保护止盈失败: ' + error.message);
            }
        }

        async function removeProtectiveProfit(ticket) {
            try {
                if (!confirm(`确定要移除订单#${ticket}的保护止盈吗？`)) {
                    return;
                }

                const response = await ui.apiCall(`/api/positions/${ticket}/protective-profit`, 'DELETE');

                ui.showAlert('success', response.message);
                loadPositionsData();
            } catch (error) {
                console.error('移除保护止盈失败:', error);
                ui.showAlert('error', '移除保护止盈失败: ' + error.message);
            }
        }

        // 警报统计相关函数
        async function refreshAlertStats() {
            if (ui) {
                await ui.loadAlertStats();
                ui.showAlert('info', '警报统计已刷新', 2000);
            }
        }

        // 页面加载时初始化监控状态
        document.addEventListener('DOMContentLoaded', function() {
            // 延迟加载监控状态，确保页面元素已创建
            setTimeout(() => {
                loadMonitoringStatus();
            }, 1000);

            // 警报统计现在自动加载所有时间段，无需切换
        });
    </script>
</body>
</html>
