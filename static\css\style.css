/* TradingView自动交易系统 - 自定义样式 */

/* 全局样式 */
body {
    background-color: #f8f9fa;
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* 深色主题样式 */
[data-theme="dark"] {
    background-color: #1a1a1a !important;
    color: #e0e0e0 !important;
}

[data-theme="dark"] body {
    background-color: #1a1a1a !important;
    color: #e0e0e0 !important;
}

[data-theme="dark"] .navbar {
    background-color: #2d2d2d !important;
    border-bottom: 1px solid #404040;
}

[data-theme="dark"] .navbar-brand,
[data-theme="dark"] .navbar-nav .nav-link {
    color: #e0e0e0 !important;
}

[data-theme="dark"] .card {
    background-color: #2d2d2d !important;
    border: 1px solid #404040 !important;
    color: #e0e0e0 !important;
}

[data-theme="dark"] .card-header {
    background: linear-gradient(135deg, #4a5568 0%, #2d3748 100%) !important;
    border-bottom: 1px solid #404040 !important;
}

[data-theme="dark"] .table {
    background-color: #2d2d2d !important;
    color: #e0e0e0 !important;
}

[data-theme="dark"] .table th,
[data-theme="dark"] .table td {
    border-color: #404040 !important;
}

[data-theme="dark"] .table-striped tbody tr:nth-of-type(odd) {
    background-color: #333333 !important;
}

[data-theme="dark"] .form-control,
[data-theme="dark"] .form-select {
    background-color: #333333 !important;
    border-color: #404040 !important;
    color: #e0e0e0 !important;
}

[data-theme="dark"] .form-control:focus,
[data-theme="dark"] .form-select:focus {
    background-color: #333333 !important;
    border-color: #667eea !important;
    color: #e0e0e0 !important;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
}

[data-theme="dark"] .btn-primary {
    background-color: #667eea !important;
    border-color: #667eea !important;
}

[data-theme="dark"] .btn-secondary {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
}

[data-theme="dark"] .btn-outline-primary {
    color: #667eea !important;
    border-color: #667eea !important;
}

[data-theme="dark"] .btn-outline-primary:hover {
    background-color: #667eea !important;
    border-color: #667eea !important;
}

[data-theme="dark"] .modal-content {
    background-color: #2d2d2d !important;
    color: #e0e0e0 !important;
}

[data-theme="dark"] .modal-header {
    border-bottom: 1px solid #404040 !important;
}

[data-theme="dark"] .modal-footer {
    border-top: 1px solid #404040 !important;
}

[data-theme="dark"] .nav-tabs {
    border-bottom: 1px solid #404040 !important;
}

[data-theme="dark"] .nav-tabs .nav-link {
    color: #e0e0e0 !important;
    border-color: transparent !important;
}

[data-theme="dark"] .nav-tabs .nav-link.active {
    background-color: #2d2d2d !important;
    border-color: #404040 #404040 #2d2d2d !important;
    color: #e0e0e0 !important;
}

[data-theme="dark"] .tab-content {
    background-color: #2d2d2d !important;
}

[data-theme="dark"] .alert-success {
    background-color: #155724 !important;
    border-color: #28a745 !important;
    color: #d4edda !important;
}

[data-theme="dark"] .alert-danger {
    background-color: #721c24 !important;
    border-color: #dc3545 !important;
    color: #f8d7da !important;
}

[data-theme="dark"] .alert-warning {
    background-color: #856404 !important;
    border-color: #ffc107 !important;
    color: #fff3cd !important;
}

[data-theme="dark"] .config-row {
    background-color: #2d2d2d !important;
    border-color: #404040 !important;
}

[data-theme="dark"] .metric-label {
    color: #a0a0a0 !important;
}

[data-theme="dark"] .system-status-footer {
    background-color: #2d2d2d !important;
    border-top: 1px solid #404040 !important;
}

/* 导航栏样式 */
.navbar-brand {
    font-weight: 600;
    font-size: 1.2rem;
}

.navbar-brand i {
    margin-right: 8px;
    color: #ffc107;
}

/* 卡片样式 */
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    border-radius: 10px;
    margin-bottom: 20px;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600;
}

.card-header h5, .card-header h6 {
    margin-bottom: 0;
}

/* 状态指示器 */
.status-indicator {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 8px;
}

.status-online {
    background-color: #28a745;
    box-shadow: 0 0 5px rgba(40, 167, 69, 0.5);
}

.status-offline {
    background-color: #dc3545;
    box-shadow: 0 0 5px rgba(220, 53, 69, 0.5);
}

.status-warning {
    background-color: #ffc107;
    box-shadow: 0 0 5px rgba(255, 193, 7, 0.5);
}

/* 指标卡片 */
.metric-card {
    text-align: center;
    padding: 20px;
    transition: transform 0.2s;
}

.metric-card:hover {
    transform: translateY(-2px);
}

.metric-value {
    font-size: 2rem;
    font-weight: 600;
    margin-bottom: 5px;
    line-height: 1.2;
}

.metric-label {
    color: #6c757d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* 盈亏颜色 */
.profit-positive {
    color: #28a745 !important;
    font-weight: 600;
}

.profit-negative {
    color: #dc3545 !important;
    font-weight: 600;
}

.profit-neutral {
    color: #6c757d !important;
    font-weight: 600;
}

/* 按钮样式 */
.btn-action {
    margin: 5px;
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.2s;
}

.btn-action:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.15);
}

/* 表格样式 */
.table-responsive {
    border-radius: 10px;
    overflow-x: auto;
    overflow-y: hidden;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    -webkit-overflow-scrolling: touch; /* iOS平滑滚动 */
}

.table {
    margin-bottom: 0;
}

.table thead th {
    background-color: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

/* 标签页样式 */
.nav-tabs {
    border-bottom: 2px solid #dee2e6;
    margin-bottom: 0;
}

.nav-tabs .nav-link {
    border: none;
    border-radius: 8px 8px 0 0;
    color: #6c757d;
    font-weight: 500;
    margin-right: 5px;
}

.nav-tabs .nav-link.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
}

.nav-tabs .nav-link:hover {
    border: none;
    color: #495057;
}

/* 徽章样式 */
.badge {
    font-size: 0.8rem;
    padding: 0.4em 0.6em;
}

/* 加载动画 */
.loading-spinner {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 9999;
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 10px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
}

/* 警告框样式 */
.alert {
    border: none;
    border-radius: 8px;
    font-weight: 500;
}

.alert-dismissible .btn-close {
    padding: 0.75rem 1rem;
}

/* 表单样式 */
.form-control {
    border-radius: 8px;
    border: 1px solid #ced4da;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-control:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

/* 配置行样式 */
.config-row {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    transition: box-shadow 0.2s;
}

.config-row:hover {
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 响应式调整 */
@media (max-width: 768px) {
    .metric-value {
        font-size: 1.5rem;
    }
    
    .btn-action {
        margin: 2px;
        font-size: 0.9rem;
    }
    
    .card {
        margin-bottom: 15px;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.fade-in {
    animation: fadeIn 0.5s ease-out;
}

/* 工具提示样式 */
.tooltip {
    font-size: 0.875rem;
}

.tooltip-inner {
    background-color: #333;
    border-radius: 6px;
}

/* 进度条样式 */
.progress {
    height: 8px;
    border-radius: 4px;
}

.progress-bar {
    border-radius: 4px;
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* 系统状态底部固定样式 */
.system-status-footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 10px 15px;
    box-shadow: 0 -2px 10px rgba(0,0,0,0.15);
    z-index: 1000;
    font-size: 0.9rem;
    border-top: 1px solid rgba(255,255,255,0.1);
}

.system-status-footer .status-item {
    display: inline-flex;
    align-items: center;
    margin-right: 25px;
    white-space: nowrap;
    background: rgba(255,255,255,0.1);
    padding: 4px 8px;
    border-radius: 6px;
    backdrop-filter: blur(10px);
}

.system-status-footer .status-item i {
    margin-right: 6px;
    font-size: 1rem;
}

.system-status-footer .status-label {
    font-weight: 500;
    margin-right: 8px;
    opacity: 0.9;
}

.system-status-footer .status-value {
    font-weight: 600;
    display: flex;
    align-items: center;
}

.system-status-footer .status-indicator {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    display: inline-block;
    margin-right: 6px;
    animation: pulse 2s infinite;
}

.system-status-footer .btn-group {
    margin-left: auto;
}

.system-status-footer .btn {
    padding: 10px 14px;
    font-size: 1.1rem;
    margin: 0 4px;
    border-radius: 8px;
    backdrop-filter: blur(10px);
    background: rgba(255,255,255,0.15);
    border: 1px solid rgba(255,255,255,0.2);
    color: white;
    transition: all 0.2s ease;
    white-space: nowrap;
    min-width: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
}

.system-status-footer .btn:hover {
    background: rgba(255,255,255,0.25);
    transform: translateY(-1px);
}

.system-status-footer .btn i {
    margin-right: 6px;
}

.system-status-footer .btn span {
    display: inline-block !important;
    font-weight: 500;
    color: white !important;
    opacity: 1 !important;
    visibility: visible !important;
}

/* 底部按钮特定颜色 */
.system-status-footer .btn-success {
    background: rgba(40, 167, 69, 0.8);
    border-color: rgba(40, 167, 69, 0.9);
}

.system-status-footer .btn-success:hover {
    background: rgba(40, 167, 69, 1);
    border-color: rgba(40, 167, 69, 1);
}

.system-status-footer .btn-warning {
    background: rgba(255, 193, 7, 0.8);
    border-color: rgba(255, 193, 7, 0.9);
    color: #212529;
}

.system-status-footer .btn-warning:hover {
    background: rgba(255, 193, 7, 1);
    border-color: rgba(255, 193, 7, 1);
    color: #212529;
}

.system-status-footer .btn-info {
    background: rgba(13, 202, 240, 0.8);
    border-color: rgba(13, 202, 240, 0.9);
}

.system-status-footer .btn-info:hover {
    background: rgba(13, 202, 240, 1);
    border-color: rgba(13, 202, 240, 1);
}

.system-status-footer .btn-secondary {
    background: rgba(108, 117, 125, 0.8);
    border-color: rgba(108, 117, 125, 0.9);
}

.system-status-footer .btn-secondary:hover {
    background: rgba(108, 117, 125, 1);
    border-color: rgba(108, 117, 125, 1);
}

.system-status-footer .btn-primary {
    background: rgba(13, 110, 253, 0.8);
    border-color: rgba(13, 110, 253, 0.9);
}

.system-status-footer .btn-primary:hover {
    background: rgba(13, 110, 253, 1);
    border-color: rgba(13, 110, 253, 1);
}

/* 禁用状态 */
.system-status-footer .btn:disabled {
    background: rgba(108, 117, 125, 0.4) !important;
    border-color: rgba(108, 117, 125, 0.4) !important;
    color: rgba(255, 255, 255, 0.5) !important;
    opacity: 0.6;
}

/* 按钮文字显示优化 */
.system-status-footer .btn span {
    font-weight: 500 !important;
    display: inline-block !important;
    color: white !important;
}

/* 强制显示按钮文字 */
.system-status-footer .btn-group .btn span {
    display: inline-block !important;
    visibility: visible !important;
    opacity: 1 !important;
    color: inherit !important;
}

/* 底部按钮专用样式 */
.system-status-footer .footer-btn {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
}

/* 自定义工具提示样式 */
.system-status-footer .btn[title]:hover::after {
    content: attr(title);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 0.8rem;
    white-space: nowrap;
    z-index: 1000;
    margin-bottom: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    backdrop-filter: blur(10px);
    animation: tooltipFadeIn 0.2s ease-out;
}

.system-status-footer .btn[title]:hover::before {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 6px solid transparent;
    border-top-color: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    margin-bottom: 2px;
}

@keyframes tooltipFadeIn {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(5px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* 确保按钮组在小屏幕上的显示 */
@media (max-width: 576px) {
    .system-status-footer .btn {
        padding: 6px 8px;
        font-size: 0.9rem;
        margin: 0 1px;
        min-width: 45px;
    }

    .system-status-footer .btn i {
        margin-right: 0;
    }
}

/* 为底部状态栏留出空间 */
body {
    padding-bottom: 60px;
}

/* 移动端调整 */
@media (max-width: 768px) {
    .system-status-footer {
        padding: 8px 10px;
        font-size: 0.8rem;
        flex-direction: column;
        gap: 8px;
    }

    .system-status-footer .d-flex {
        flex-direction: column;
        gap: 8px;
    }

    .system-status-footer .status-item {
        margin-right: 12px;
        padding: 3px 6px;
        font-size: 0.75rem;
    }

    .system-status-footer .status-item i {
        font-size: 0.9rem;
        margin-right: 4px;
    }

    .system-status-footer .btn {
        padding: 8px 10px;
        font-size: 1rem;
        margin: 0 2px;
        min-width: 50px;
    }

    .system-status-footer .btn i {
        margin-right: 3px;
    }

    .system-status-footer .btn-group {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 4px;
    }

    body {
        padding-bottom: 70px;
    }
}

/* 移动端表格优化 - 简化版本 */
@media (max-width: 768px) {
    /* 强制所有表格容器可以横向滚动 */
    .table-responsive {
        overflow-x: auto !important;
        overflow-y: hidden !important;
        -webkit-overflow-scrolling: touch !important;
        width: 100% !important;
        display: block !important;
    }

    /* 强制所有表格有最小宽度 */
    .table {
        min-width: 800px !important;
        width: auto !important;
        margin-bottom: 0 !important;
    }

    /* 强制显示所有隐藏的列 */
    .d-none.d-md-table-cell,
    .d-none.d-lg-table-cell,
    .d-none.d-sm-table-cell {
        display: table-cell !important;
    }

    /* 表格单元格优化 */
    .table th,
    .table td {
        white-space: nowrap !important;
        padding: 0.3rem 0.2rem !important;
        font-size: 0.75rem !important;
        min-width: 60px !important;
    }

    /* 操作按钮优化 */
    .btn-sm {
        padding: 0.1rem 0.2rem !important;
        font-size: 0.6rem !important;
        margin: 1px !important;
        min-width: 25px !important;
        line-height: 1.2 !important;
    }

    /* 按钮组不换行 */
    .btn-group {
        white-space: nowrap !important;
        display: inline-flex !important;
        gap: 1px !important;
    }

    /* 操作列内的按钮组特殊优化 */
    .table td:last-child .btn-group {
        justify-content: center !important;
        width: 100% !important;
    }

    /* 操作列内的按钮 */
    .table td:last-child .btn {
        flex: 1 !important;
        max-width: 35px !important;
        padding: 0.1rem 0.15rem !important;
        font-size: 0.55rem !important;
    }

    /* 操作列粘性定位 */
    .table th:last-child,
    .table td:last-child {
        position: sticky !important;
        right: 0 !important;
        background-color: white !important;
        z-index: 10 !important;
        border-left: 1px solid #dee2e6 !important;
        min-width: 80px !important;
        max-width: 90px !important;
        width: 85px !important;
    }

    /* 表头粘性定位 */
    .table thead th {
        position: sticky !important;
        top: 0 !important;
        background-color: #f8f9fa !important;
        z-index: 5 !important;
    }

    /* 操作列表头 */
    .table thead th:last-child {
        z-index: 15 !important;
    }

    /* 滚动提示 */
    .table-responsive::after {
        content: "← 左右滑动查看更多 →";
        position: absolute;
        bottom: -25px;
        left: 50%;
        transform: translateX(-50%);
        font-size: 0.75rem;
        color: #6c757d;
        white-space: nowrap;
        pointer-events: none;
        opacity: 0.7;
    }

    /* 当表格内容超出时显示滚动提示 */
    .table-responsive[data-scrollable="true"]::after {
        display: block;
    }

    /* 触摸滚动增强 */
    .table-responsive {
        scroll-behavior: smooth;
        scrollbar-width: thin;
        scrollbar-color: #c1c1c1 transparent;
    }

    /* 移动端滚动条样式 */
    .table-responsive::-webkit-scrollbar,
    div:has(> table)::-webkit-scrollbar {
        height: 8px;
    }

    .table-responsive::-webkit-scrollbar-track,
    div:has(> table)::-webkit-scrollbar-track {
        background: rgba(0,0,0,0.1);
        border-radius: 4px;
    }

    .table-responsive::-webkit-scrollbar-thumb,
    div:has(> table)::-webkit-scrollbar-thumb {
        background: rgba(0,0,0,0.4);
        border-radius: 4px;
    }

    .table-responsive::-webkit-scrollbar-thumb:hover,
    div:has(> table)::-webkit-scrollbar-thumb:hover {
        background: rgba(0,0,0,0.6);
    }

    /* 强制所有包含表格的div都可以滚动 */
    div:has(> table.table) {
        overflow-x: auto !important;
        overflow-y: hidden !important;
        -webkit-overflow-scrolling: touch !important;
        max-width: 100% !important;
    }

    /* 特别针对持仓表格的容器 */
    #positionsTable,
    table:has(#positionsDetailTableBody),
    table:has(th:contains("订单号")),
    table:has(th:contains("交易对")) {
        min-width: 950px !important;
    }

    /* 确保所有表格单元格内容不换行 */
    .table th *,
    .table td * {
        white-space: nowrap !important;
    }

    /* 按钮组强制不换行 */
    .table .btn-group,
    .table .btn-group-sm {
        display: inline-flex !important;
        flex-wrap: nowrap !important;
        white-space: nowrap !important;
    }

    /* 操作按钮优化 */
    .table .btn {
        font-size: 0.7rem !important;
        padding: 0.2rem 0.3rem !important;
        margin: 0 1px !important;
        min-width: 30px !important;
    }
}
